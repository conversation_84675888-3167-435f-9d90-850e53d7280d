<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影文本分析概要 - 第三章</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.3em;
        }
        h1 {
            text-align: center;
            font-size: 2.2em;
            color: #1a365d;
        }
        h2 {
            font-size: 1.8em;
            color: #2c5282;
        }
        h3 {
            font-size: 1.5em;
            color: #2b6cb0;
        }
        blockquote {
            background-color: #f0f4f8;
            border-left: 4px solid #4299e1;
            padding: 10px 15px;
            margin: 20px 0;
        }
        code {
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background-color: #ebf4ff;
            padding: 2px 0;
        }
        .concept {
            color: #3182ce;
            font-weight: bold;
        }
        .example {
            background-color: #e6fffa;
            padding: 15px;
            border-left: 4px solid #38b2ac;
            margin: 20px 0;
        }
        .note {
            background-color: #fffaf0;
            padding: 15px;
            border-left: 4px solid #ed8936;
            margin: 20px 0;
        }
        .diagram {
            text-align: center;
            margin: 20px 0;
        }
        .reference {
            font-size: 0.9em;
            color: #718096;
        }
        .section {
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <h1>第三章 文本分析——一个引人争议的模式</h1>
    
    <div class="section">
        <p>电影分析中，分析者面临多种选择可能性：分析工具、分析客体以及分析途径等。在这种多样性面前，"文本分析"(analyse textuelle)的产生就是为了应对可能出现的散漫（在客体选择上）和不确定（在方法选择上）。文本分析在当代电影分析中占据重要地位，主要基于两个原因：</p>
        <ul>
            <li>"文本"(texte)概念对影片及影片分析的统一性提出了根本问题</li>
            <li>文本分析曾几乎成为影片分析的一般代名词</li>
        </ul>
    </div>

    <div class="section">
        <h2>一、文本分析与结构主义</h2>
        
        <h3>1. 基本概念</h3>
        <p>结构主义(structuralisme)是20世纪60年代盛行的思潮，其核心是"结构"概念。结构主义分析家尝试辨明隐藏在意义生产过程中、能够解释外在表现形式的"<span class="concept">深层结构</span>"(structure "profonde")。</p>
        
        <p>法国人类学家列维·斯特劳斯(Claude Lévi-Strauss)是结构主义的重要代表。他通过研究大量表面看似复杂武断的神话，发现其中存在规律性和系统性——这正是这些神话"深层结构"的特质。他推论出，表面上极其不同的意义生产活动，实际上可能共享相同的结构。</p>
        
        <blockquote>
            "不论在思辨层面或实践层面，差别的明显性较其内容更重要得多：只要它存在，就会形成一种有用的区别系统，如同一个可以运用在一个初窥乍看之下完续而难以理解的文本之上的架构，加以切割、造成对比，亦即引进讯息的形式条件，以解读这个文本。"——列维·斯特劳斯《野性的思维》
        </blockquote>
        
        <p>列维·斯特劳斯所指的架构、区别系统，就是所谓的文本分析。其分析方法让我们联想到语言学研究，即通过切分来辨识意义上的对立（如索绪尔提出的语言[langue]与言语[parole]之间的区别）。</p>
        
        <p>"结构"语言学是结构主义的重要参考坐标和理论泉源。结构经常被视为<span class="concept">二元对立系统</span>(oppositions binaires)。由于语言被视为所有意义生产过程的基础架构，这强化了语言学在结构主义发展中的奠基角色。列维·斯特劳斯致力于建立"神话素"(mythemes)体系，而拉康(Jacques Lacan)则宣称"无意识有如语言活动一般的结构"。</p>
        
        <h3>2. 结构主义分析</h3>
        <p>从神话到无意识，再到历史限定的文学或艺术创作（如电影），对于任何重要的意义生产活动，我们都可以运用结构主义进行分析。电影的文本分析衍生自结构主义分析，尽管两者关系有时并不稳定。</p>
        
        <p>列维·斯特劳斯本人较少将结构主义分析应用于文学艺术作品，他认为神话与文学之间可能的类比只有诗歌。1962年，他与罗曼·雅各布森(Roman Jakobson)合作分析法国诗人波德莱尔的《猫》，但这类分析并未立即引起广泛共鸣。</p>
        
        <p>让-保罗·杜蒙(Jean-Paul Dumont)与让·莫诺(Jean Monod)对斯坦利·库布里克的《2001年：太空漫游》的分析被视为最彻底的"列维·斯特劳斯式"影片分析。他们尝试"尽可能少用词汇及文法成分"来厘清该片的"语意结构"，从"新版"星宿神话角度解析。整个分析重点放在影片声带部分，而非影像（这对一部高度视觉化的电影来说有些不合适）。他们按照片中叙事时序，在对立/区别系统下组织具代表性的元素，体现"从方法论上否认最终意义的存在"这一结构主义特点，而以"语言活动内部能指与能指之间的关系"构成的意旨作用代替。</p>
        
        <p>除列维·斯特劳斯外，艾柯(Umberto Eco)、罗兰·巴特(Roland Barthes)和梅斯(Christian Metz)三位理论家对文本分析思潮影响深远。艾柯在《不在的结构》(1968)中提出意义与沟通现象（包含文艺作品）组成符号系统的观念，并详细阐述了影像符码定义。巴特的影响则更加直接明显，他的《影像的修辞》分析广告影像，侧重意义层面而非沟通层面，探究"内涵意旨"(connotation)在影像内在意义网络中的地位。</p>
        
        <p>巴特早在《神话学》(1957)中就分析了各种"文本"内部的意识形态表现，指出社会流传的产品（包括电影）承载着系统化的意涵。1960年，他在《影片学国际月刊》上发表文章，系统提出电影结构主义分析原则，质疑："一部影片内涵意义的位置、形式与效果为何？影片中的一切都是有意义的吗？还是影片的能指不连贯？结合影片中能指与所指关系的真正性质是什么？"</p>
        
        <p>梅斯在《语言活动与电影》(1971)中尝试系统回答这些问题。他定义的<span class="concept">符码</span>概念涵盖所有影片表意作用的规律及系统化现象，相当于替代了电影的"语言"，尽管不是绝对对等关系。理论上，每个符码都可独立成"纯粹状态"，但永远以共生方式结合运作。符码让我们描述电影语言活动内部的多重表意功能，即使部分符码比其他更"基本"、更重要（如运动类比关系符码），它们也不能像语言一样组织或承载直接的外延意义。符码既可检视特定影片中与大部分电影共通的普遍现象（如具象类比关系），也可检视局部现象（如好莱坞电影中的"背景投影法"），并界定影片外围的文化条件（如类型电影、社会再现等）。在影片学中，符码成为结构主义最具代表性的概念。</p>
    </div>
</body>
</html> 