<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影文本分析概要 - 第三章（第三部分）</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.3em;
        }
        h1 {
            text-align: center;
            font-size: 2.2em;
            color: #1a365d;
        }
        h2 {
            font-size: 1.8em;
            color: #2c5282;
        }
        h3 {
            font-size: 1.5em;
            color: #2b6cb0;
        }
        blockquote {
            background-color: #f0f4f8;
            border-left: 4px solid #4299e1;
            padding: 10px 15px;
            margin: 20px 0;
        }
        code {
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background-color: #ebf4ff;
            padding: 2px 0;
        }
        .concept {
            color: #3182ce;
            font-weight: bold;
        }
        .example {
            background-color: #e6fffa;
            padding: 15px;
            border-left: 4px solid #38b2ac;
            margin: 20px 0;
        }
        .note {
            background-color: #fffaf0;
            padding: 15px;
            border-left: 4px solid #ed8936;
            margin: 20px 0;
        }
        .diagram {
            text-align: center;
            margin: 20px 0;
        }
        svg {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <h1>第三章 文本分析——一个引人争议的模式（第三部分）</h1>
    
    <div class="section">
        <h2>三、影片符码分析</h2>
        
        <h3>1. 符码概念的实质意义</h3>
        <p>符码概念虽然至今仍具相当理论价值——它假设影片在完整系统上存在许多独立自主的意义层次——已成为影片分析必备共识，但在实质应用上没有那么明显的力量。由于符码概念普遍性很高，无法成为立竿见影的实用工具。</p>
        
        <p>符码系统分析带来的问题至少有三种：</p>
        <ol>
            <li>不是所有符码都具有同等重要性（具体运用时能看出它们的不对等关系），各个符码概念在普遍性方面呈现异质化现象；</li>
            <li>符码从不以"纯粹"状态出现，因为影片既是符码具体体现的成果，也是符码系统支配规则形成的所在：影片能创造符码，就如同能运用符码一样。因此，要具体"孤立"出符码经常很困难；</li>
            <li>当衡量影片艺术内在特质时，运用符码概念多少显得力不从心。伟大影片总是充满原创性，是对古典手法的彻底反动与决裂，因此符码分析基本上更适用于系列式影片或"一般"电影。</li>
        </ol>
        
        <h3>2. 影片分析与符码系统分析</h3>
        <p>前面详谈过的影片分段方法——一种具体可用的做法——本质上是以理论姿态落实到影片分析范畴，借此证明电影中确实存在符码。早期"符码"分析范例也顺应这一走向，先集中在影片分段问题分析上。</p>
        
        <p>首个将"<span class="concept">大组合段</span>"模式应用于分析研究的是梅斯本人，他选择法国导演雅克·罗齐耶的《再见菲律宾》作为分析对象。梅斯将影片完整分段，把每个段落(segment)归入大组合段界定的七种符码类型之一，标出段落间的标点符号与界限。在分段过程中，梅斯特别指出段落始终界定（从影片最开头处）和段落性质确定（景和场之间的分别，或交替轮换等问题）方面的困难。</p>
        
        <p>除了从影片本身导出的结论外，这篇分析的最终目的是"检视"大组合段类型，加以琢磨、讨论并修正。通过《再见菲律宾》的分段分析，明确表示大组合段（不像影像类比关系符码）并非"绝对的"符码，它只是这个语言系统在理论及实务发展过程中的一个历史阶段（大致符合"古典时期"）。在特定影片中，某类组合段出现频率多寡，取决于该片在电影形式发展史中的地位。相对地，分析者可借由组合段出现频率描绘该片风格特性。</p>
        
        <blockquote>
            "从《再见菲律宾》一片各种段落类型出现频率多寡的概述过程中，我们可以重新确认在直观批评上所感受到的影片风格——一部典型的'新电影'（形式解放、对过度'修辞'手法的反动、影片叙事呈现'简单化'、'透明化'趋向等），以及存在于这类新电影之内，我们可以称之为'戈达尔/直接电影'(Godard-cinema direct)的趋势（强调语言成分、场景重要性、整体'写实精神'和真正新形态蒙太奇观念的诞生）。"
        </blockquote>
        
        <p>雷蒙·贝卢尔对文森特·明尼里的《金粉世界》所作的分析，也针对这个问题进行全面再检讨（其文标题"分段与分析"[Segmenter/Analyser]已明确标举论述企图），在理论和分析两个层次目标上，将这部片子从头到尾做了完整分镜表。贝卢尔反思分段过程中遇到的困难（尽管该片"古典程度"很高），指出"以影片能指层面中多重的直接时间作为段落区分标准，只能局部地符合叙事情节开展过程及戏剧行动的时间序列"。</p>
        
        <p>因此，贝卢尔建议同时考虑使用与剧情叙事相对应的"<span class="concept">超段落</span>"单元(unités sur-segmentales，梅斯在《再见菲律宾》分析中已试探这种可能性)，以及同一段落中由于"轻微的"情节改变而再加区分的"<span class="concept">次段落</span>"单元(unités sous-segmentales，例如某一剧中人物中途出现或离开)，来处理属于古典传统的叙事电影。</p>
        
        <div class="diagram">
            <svg width="600" height="320" viewBox="0 0 600 320">
                <!-- 超段落 -->
                <rect x="50" y="20" width="500" height="60" rx="5" ry="5" fill="#edf2f7" stroke="#4a5568" stroke-width="2"/>
                <text x="300" y="55" text-anchor="middle" font-size="18" fill="#2d3748">超段落 (剧情叙事单元)</text>
                
                <!-- 大组合段 -->
                <rect x="100" y="120" width="400" height="60" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                <text x="300" y="155" text-anchor="middle" font-size="18" fill="#2d3748">大组合段 (影片符码)</text>
                
                <!-- 次段落 -->
                <rect x="150" y="220" width="300" height="60" rx="5" ry="5" fill="#edf2f7" stroke="#4a5568" stroke-width="2"/>
                <text x="300" y="255" text-anchor="middle" font-size="18" fill="#2d3748">次段落 (情节微小变化)</text>
                
                <!-- 连接线 -->
                <line x1="300" y1="80" x2="300" y2="120" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="300" y1="180" x2="300" y2="220" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                
                <!-- 箭头文字 -->
                <text x="320" y="105" font-size="12" fill="#4a5568">分解</text>
                <text x="320" y="205" font-size="12" fill="#4a5568">细分</text>
            </svg>
        </div>
        
        <p>贝卢尔的建议具有重要理论价值（几乎所有叙事电影都可运用这种分段方式），特别是它明确指出，大组合段实际上是分析分段活动的一种面向（一种符码），一头连接叙事符码（超段落），另一头（次段落）则连接多种随着分段愈来愈精密而起作用的符码，进而剖析仅包含少数能指构成的小文本片段。</p>
        
        <p>除了这两篇奠基性学术论文外，还有更多篇章援用大组合段或贝卢尔模式来分析电影，但它们都没有超越这两篇论文，而一部电影的（超/次）段落区分也一直是评鉴其风格归属的有力指标。</p>
        
        <p>另一个与影片分析如影随形、所有电影符码分析必然关心的问题是符码的实效性。米歇尔·马利对阿兰·雷奈的《穆里爱》所作分析就明显触及这个问题。在这篇从多角度勘验整部影片的通盘分析中，特别开辟章节探讨该片的"声音符码"问题，其两个分析重点具有普遍性：</p>
        
        <ol>
            <li>"突显声音分析轴线的重要性，并不代表这个轴线是独立自主的"，所以该篇分析结论之一是影片中配乐不具独立自主功能，它只有在对应整部影片时才有意义；</li>
            <li>"声音符码"具有复数形式，因为它涉及范围涵括音响构成、音画之间关系（视听两向度相互配合）等声音类比关系问题，以及影片对白问题，因此这个分析轴不会是单一方向的。</li>
        </ol>
        
        <p>影片声音研究角度与分段问题研究角度不同，如果从整体性及多重性上掌握声音符码，会比做音响分段得到的成果好得多。从这个认知角度出发，电影配乐介入方式剖析能借由强烈抽象结构捕捉整体影片意蕴，从后制作(postsynchronisation，探讨影片与写实再现的关系)或画外音角度分析影片收音方式，也能更加丰富分析结果。</p>
        
        <p>马利在声音分析章节中，花很长篇幅定义四种"声音符码"并归类，呈现理论化走向。从"符码"角度研究电影的析论不少，它们经常超越原先预设蓝图，侧面进行与最初架构有关联的其他方面研究，进一步扩展到符码本质问题研究。</p>
        
        <p>雷蒙·贝卢尔发表过《明显性与符码》(L'Evidence et le Code)一文，专门分析霍华德·霍克斯《夜长梦多》(1946)中一个很短段落（仅12个镜头）。他选择的分析客体篇幅短小、结构简单（相当于大组合段分类中的一场戏）、视觉内容减至最低程度（一系列正/反拍镜头[champ/contrechamp]）。从对白和视线运作的重点分析中，贝卢尔指出这个段落如何通过两个演员之间的话语及视线交换，呈现"传统"好莱坞电影的剪接逻辑。除了这个特殊符码研究外，分析家更感兴趣的是编码化(code)的好莱坞电影如何在"明显性"外衣下掩藏其编码系统。</p>
        
        <p>正如前面所述，影片分析多少都有贴近理论的企图，而符码的严谨研究更加强化了这种趋向。</p>
    </div>
</body>
</html> 