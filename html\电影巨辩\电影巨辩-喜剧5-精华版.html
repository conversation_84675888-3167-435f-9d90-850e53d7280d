<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影巨辩：喜剧的现状与反思 - 深度分析</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', 'Arial', sans-serif;
            line-height: 1.8;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        header {
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            color: white;
            text-align: center;
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="60" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .subtitle {
            font-size: 1.4em;
            opacity: 0.9;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 60px 40px;
        }

        .toc {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .toc h2 {
            font-size: 2em;
            margin-bottom: 25px;
            text-align: center;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .toc-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 15px;
            list-style: none;
        }

        .toc-item {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .toc-item:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .toc-item a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            display: block;
        }

        .section {
            margin-bottom: 60px;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #667eea;
        }

        .section h2 {
            color: #1e3c72;
            font-size: 2.5em;
            margin-bottom: 30px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .section h3 {
            color: #2a5298;
            font-size: 1.8em;
            margin: 30px 0 20px 0;
            border-left: 4px solid #f093fb;
            padding-left: 20px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            border-left: 4px solid #667eea;
            padding: 25px;
            margin: 25px 0;
            border-radius: 10px;
            font-style: italic;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .quote {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-left: 5px solid #ff6b6b;
            padding: 25px;
            margin: 25px 0;
            border-radius: 10px;
            font-style: italic;
            position: relative;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        }

        .quote::before {
            content: '"';
            font-size: 4em;
            color: #ff6b6b;
            position: absolute;
            top: -10px;
            left: 15px;
            opacity: 0.3;
        }

        .analysis-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .analysis-box h4 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #ffd700;
        }

        .key-point {
            background: linear-gradient(45deg, #fa709a, #fee140);
            color: #2c3e50;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .director-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.5);
        }

        .director-card h4 {
            color: #d63384;
            font-size: 1.4em;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .movie-analysis {
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .movie-analysis h4 {
            color: #0d6efd;
            font-size: 1.4em;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .comparison-table th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }

        .comparison-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .svg-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        p {
            margin-bottom: 20px;
            text-align: justify;
            text-indent: 2em;
        }

        strong {
            color: #1e3c72;
            font-weight: bold;
        }

        em {
            color: #d63384;
            font-style: italic;
        }

        .trend-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: 2px solid #ff8a80;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .trend-box h4 {
            color: #d32f2f;
            font-size: 1.4em;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            header {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2.5em;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .section {
                padding: 25px 20px;
                margin-bottom: 30px;
            }
            
            .toc-list {
                grid-template-columns: 1fr;
            }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .scroll-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .scroll-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>电影巨辩：喜剧的现状与反思</h1>
            <div class="subtitle">从贾玲现象到中国喜剧电影的深层思考</div>
        </header>

        <div class="content">
            <div class="toc">
                <h2>🎬 内容导航</h2>
                <ul class="toc-list">
                    <li class="toc-item"><a href="#jia-ling">🎭 贾玲电影深度解析</a></li>
                    <li class="toc-item"><a href="#hybrid-comedy">🎪 混搭喜剧的挑战</a></li>
                    <li class="toc-item"><a href="#hk-comedy">🎨 港式喜剧的困境</a></li>
                    <li class="toc-item"><a href="#comedy-stars">⭐ 喜剧明星群像</a></li>
                    <li class="toc-item"><a href="#industry-analysis">📊 产业现状反思</a></li>
                    <li class="toc-item"><a href="#future-outlook">🔮 时代变迁与展望</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html> 