<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影理论与批评教程：基于《罗曼蒂克消亡史》的多维度知识体系</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.8;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 50px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: slide 20s linear infinite;
        }
        
        @keyframes slide {
            0% { transform: translateX(-50px); }
            100% { transform: translateX(50px); }
        }
        
        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 1rem;
            z-index: 2;
            position: relative;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.4rem;
            opacity: 0.95;
            z-index: 2;
            position: relative;
            margin-bottom: 0.5rem;
        }
        
        .header .description {
            font-size: 1.1rem;
            opacity: 0.85;
            z-index: 2;
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .nav-menu {
            background: #2c3e50;
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav-links {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            justify-content: center;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .nav-links a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        
        .content {
            padding: 2rem;
        }
        
        .chapter {
            margin-bottom: 4rem;
            padding: 2rem;
            border-radius: 15px;
            background: #f8f9fa;
            border-left: 6px solid #e74c3c;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }
        
        .chapter h2 {
            color: #2c3e50;
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 0.8rem;
            position: relative;
        }
        
        .chapter h2::before {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100px;
            height: 3px;
            background: #3498db;
        }
        
        .section {
            margin-bottom: 2.5rem;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.6rem;
            margin-bottom: 1rem;
            border-left: 4px solid #3498db;
            padding-left: 1rem;
        }
        
        .section h4 {
            color: #2c3e50;
            font-size: 1.3rem;
            margin: 1.5rem 0 0.8rem 0;
            font-weight: 600;
        }
        
        .highlight {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 0.3rem 0.6rem;
            border-radius: 5px;
            font-weight: bold;
            color: #2d3436;
        }
        
        .quote {
            background: #ecf0f1;
            border-left: 5px solid #3498db;
            padding: 1.5rem;
            margin: 1.5rem 0;
            font-style: italic;
            border-radius: 0 8px 8px 0;
            font-size: 1.05rem;
        }
        
        .important-box {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1.5rem 0;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        
        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .concept-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border: 2px solid transparent;
        }
        
        .concept-card:hover {
            transform: translateY(-5px);
            border-color: #3498db;
        }
        
        .concept-card h5 {
            color: #2c3e50;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .timeline-container {
            position: relative;
            margin: 2rem 0;
            padding: 1rem 0;
        }
        
        .timeline-item {
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #e74c3c;
        }
        
        .timeline-year {
            font-weight: bold;
            color: #e74c3c;
            font-size: 1.1rem;
        }
        
        .theory-box {
            background: #f1f2f6;
            border: 2px solid #3498db;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1.5rem 0;
        }
        
        .theory-box h4 {
            color: #3498db;
            margin-bottom: 1rem;
        }
        
        p {
            margin-bottom: 1rem;
            text-align: justify;
            text-indent: 2em;
            font-size: 1.05rem;
        }
        
        ul {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        
        li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }
        
        .overview {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 3rem;
            text-align: center;
        }
        
        .overview h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            border: none;
            padding: 0;
        }
        
        .overview h2::before {
            display: none;
        }
        
        .toc {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 3rem;
            border: 2px solid #e9ecef;
        }
        
        .toc h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: none;
            padding: 0;
        }
        
        .toc h2::before {
            display: none;
        }
        
        .toc-list {
            columns: 2;
            column-gap: 3rem;
            list-style: none;
            padding: 0;
        }
        
        .toc-list li {
            margin-bottom: 1rem;
            break-inside: avoid;
            padding: 0.5rem;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .toc-list li:hover {
            background: #e9ecef;
        }
        
        .toc-list a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .toc-level-1 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #e74c3c;
            margin-top: 1rem;
        }
        
        .toc-level-2 {
            font-size: 1rem;
            margin-left: 1rem;
            color: #3498db;
        }
        
        .toc-level-3 {
            font-size: 0.9rem;
            margin-left: 2rem;
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .header .subtitle {
                font-size: 1.1rem;
            }
            
            .nav-links {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .toc-list {
                columns: 1;
            }
            
            .concept-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>电影理论与批评教程</h1>
            <div class="subtitle">基于《罗曼蒂克消亡史》的多维度知识体系</div>
            <div class="description">
                从一部电影的深度解析，构建完整的电影学、历史学、社会学知识框架
            </div>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-content">
                <div class="nav-links">
                    <a href="#overview">课程概述</a>
                    <a href="#theory">电影理论</a>
                    <a href="#narrative">叙事技法</a>
                    <a href="#history">电影史</a>
                    <a href="#culture">历史文化</a>
                    <a href="#performance">表演艺术</a>
                    <a href="#visual">视听语言</a>
                    <a href="#theme">主题思想</a>
                    <a href="#genre">类型研究</a>
                </div>
            </div>
        </nav>
        
        <div class="content">
            <div id="overview" class="overview">
                <h2>课程概述</h2>
                <p style="text-indent: 0; font-size: 1.1rem;">
                    本教程以《电影巨辩》对《罗曼蒂克消亡史》的深度解析为基础，构建了一个涵盖电影理论、叙事技法、历史文化、表演艺术等多个维度的综合知识体系。通过"从一部电影看世界"的方法论，我们将学习如何从单一作品中提取和延展出丰富的学术知识点。
                </p>
            </div>
            
            <div class="toc">
                <h2>目录</h2>
                <ul class="toc-list">
                    <li class="toc-level-1"><a href="#theory">第一章：电影理论与批评方法论</a></li>
                    <li class="toc-level-2"><a href="#criticism">1.1 电影批评的意义与方法</a></li>
                    <li class="toc-level-2"><a href="#auteur">1.2 作者论与导演研究</a></li>
                    <li class="toc-level-2"><a href="#ultimate">1.3 "终极表达"理论</a></li>
                    
                    <li class="toc-level-1"><a href="#narrative">第二章：叙事理论与技法分析</a></li>
                    <li class="toc-level-2"><a href="#nonlinear">2.1 非线性叙事结构</a></li>
                    <li class="toc-level-2"><a href="#hollywood">2.2 古典好莱坞叙事技法</a></li>
                    <li class="toc-level-2"><a href="#motif">2.3 母题建构与贯穿</a></li>
                    
                    <li class="toc-level-1"><a href="#history">第三章：中国电影史与时代背景</a></li>
                    <li class="toc-level-2"><a href="#phases">3.1 中国电影发展阶段论</a></li>
                    <li class="toc-level-2"><a href="#censorship">3.2 审查制度与创作博弈</a></li>
                    <li class="toc-level-2"><a href="#generations">3.3 代际导演比较研究</a></li>
                    
                    <li class="toc-level-1"><a href="#culture">第四章：历史文化与社会背景</a></li>
                    <li class="toc-level-2"><a href="#republic">4.1 民国史观与"黄金十年"</a></li>
                    <li class="toc-level-2"><a href="#gangs">4.2 帮会文化与社会结构</a></li>
                    <li class="toc-level-2"><a href="#modernity">4.3 现代性与传统的辩证</a></li>
                    
                    <li class="toc-level-1"><a href="#performance">第五章：表演艺术与演员分析</a></li>
                    <li class="toc-level-2"><a href="#acting">5.1 表演方法论</a></li>
                    <li class="toc-level-2"><a href="#asano">5.2 浅野忠信表演分析</a></li>
                    <li class="toc-level-2"><a href="#complexity">5.3 人物复杂性构建</a></li>
                    
                    <li class="toc-level-1"><a href="#visual">第六章：视听语言与美学风格</a></li>
                    <li class="toc-level-2"><a href="#cinematography">6.1 摄影美学</a></li>
                    <li class="toc-level-2"><a href="#production">6.2 服化道设计哲学</a></li>
                    <li class="toc-level-2"><a href="#gaze">6.3 凝视理论与观者地位</a></li>
                    
                    <li class="toc-level-1"><a href="#theme">第七章：主题思想与哲学内核</a></li>
                    <li class="toc-level-2"><a href="#truth">7.1 "真与假"的母题</a></li>
                    <li class="toc-level-2"><a href="#nostalgia">7.2 时代缅怀与价值判断</a></li>
                    <li class="toc-level-2"><a href="#romanticism">7.3 "罗曼蒂克"的深层含义</a></li>
                    
                    <li class="toc-level-1"><a href="#genre">第八章：类型片研究：黑帮片分析</a></li>
                    <li class="toc-level-2"><a href="#function">8.1 黑帮片的功能与意义</a></li>
                    <li class="toc-level-2"><a href="#historical">8.2 历史人物的影像化</a></li>
                    <li class="toc-level-2"><a href="#comparison">8.3 中西黑帮片比较</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html> 