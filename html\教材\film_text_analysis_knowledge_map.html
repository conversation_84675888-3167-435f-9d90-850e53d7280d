<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影文本分析知识图谱</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.3em;
        }
        h1 {
            text-align: center;
            font-size: 2.2em;
            color: #1a365d;
        }
        h2 {
            font-size: 1.8em;
            color: #2c5282;
        }
        h3 {
            font-size: 1.5em;
            color: #2b6cb0;
        }
        blockquote {
            background-color: #f0f4f8;
            border-left: 4px solid #4299e1;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #ebf4ff;
            padding: 2px 0;
        }
        .concept {
            color: #3182ce;
            font-weight: bold;
        }
        .example {
            background-color: #e6fffa;
            padding: 15px;
            border-left: 4px solid #38b2ac;
            margin: 20px 0;
        }
        .note {
            background-color: #fffaf0;
            padding: 15px;
            border-left: 4px solid #ed8936;
            margin: 20px 0;
        }
        .diagram {
            text-align: center;
            margin: 20px 0;
        }
        svg {
            max-width: 100%;
            height: auto;
        }
        .knowledge-map {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }
        .knowledge-section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .concept-box {
            background-color: #e2e8f0;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #4a5568;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .grid-item {
            background-color: #f7fafc;
            border-radius: 5px;
            padding: 15px;
            border: 1px solid #e2e8f0;
        }
        .theorist {
            background-color: #ebf8ff;
            border-left: 4px solid #3182ce;
            padding: 10px 15px;
            margin: 10px 0;
        }
        .timeline {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 20px 0;
            position: relative;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: #4a5568;
        }
        .timeline-item {
            margin-left: 30px;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            position: relative;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 15px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #4a5568;
        }
    </style>
</head>
<body>
    <h1>电影文本分析知识图谱</h1>
    
    <div class="knowledge-map">
        <div class="knowledge-section">
            <h2>一、文本分析与结构主义的理论基础</h2>
            
            <div class="diagram">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- 结构主义基础 -->
                    <rect x="50" y="20" width="700" height="80" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                    <text x="400" y="45" text-anchor="middle" font-size="18" fill="#2d3748">结构主义思潮 (structuralisme)</text>
                    <text x="400" y="75" text-anchor="middle" font-size="14" fill="#4a5568">核心：探寻隐藏在表象背后的"深层结构"(structure "profonde")</text>
                    
                    <!-- 左支分支 -->
                    <line x1="200" y1="100" x2="200" y2="130" stroke="#4a5568" stroke-width="2"/>
                    <rect x="50" y="130" width="300" height="70" rx="5" ry="5" fill="#edf2f7" stroke="#4a5568" stroke-width="2"/>
                    <text x="200" y="160" text-anchor="middle" font-size="16" fill="#2d3748">二元对立系统 (oppositions binaires)</text>
                    <text x="200" y="185" text-anchor="middle" font-size="14" fill="#4a5568">结构经常被理解为对立/区别系统</text>
                    
                    <!-- 右支分支 -->
                    <line x1="600" y1="100" x2="600" y2="130" stroke="#4a5568" stroke-width="2"/>
                    <rect x="450" y="130" width="300" height="70" rx="5" ry="5" fill="#edf2f7" stroke="#4a5568" stroke-width="2"/>
                    <text x="600" y="160" text-anchor="middle" font-size="16" fill="#2d3748">语言学基础</text>
                    <text x="600" y="185" text-anchor="middle" font-size="14" fill="#4a5568">索绪尔的语言(langue)与言语(parole)区分</text>
                    
                    <!-- 理论家 -->
                    <line x1="150" y1="200" x2="150" y2="230" stroke="#4a5568" stroke-width="2"/>
                    <rect x="50" y="230" width="200" height="60" rx="5" ry="5" fill="#e6fffa" stroke="#38b2ac" stroke-width="2"/>
                    <text x="150" y="260" text-anchor="middle" font-size="14" fill="#2d3748">列维·斯特劳斯</text>
                    <text x="150" y="280" text-anchor="middle" font-size="12" fill="#4a5568">神话素(mythemes)分析</text>
                    
                    <line x1="300" y1="200" x2="300" y2="230" stroke="#4a5568" stroke-width="2"/>
                    <rect x="250" y="230" width="200" height="60" rx="5" ry="5" fill="#e6fffa" stroke="#38b2ac" stroke-width="2"/>
                    <text x="350" y="260" text-anchor="middle" font-size="14" fill="#2d3748">艾柯 (Umberto Eco)</text>
                    <text x="350" y="280" text-anchor="middle" font-size="12" fill="#4a5568">符号系统与意义沟通</text>
                    
                    <line x1="500" y1="200" x2="500" y2="230" stroke="#4a5568" stroke-width="2"/>
                    <rect x="450" y="230" width="200" height="60" rx="5" ry="5" fill="#e6fffa" stroke="#38b2ac" stroke-width="2"/>
                    <text x="550" y="260" text-anchor="middle" font-size="14" fill="#2d3748">罗兰·巴特 (Roland Barthes)</text>
                    <text x="550" y="280" text-anchor="middle" font-size="12" fill="#4a5568">内涵意指(connotation)分析</text>
                    
                    <line x1="700" y1="200" x2="700" y2="230" stroke="#4a5568" stroke-width="2"/>
                    <rect x="650" y="230" width="100" height="60" rx="5" ry="5" fill="#e6fffa" stroke="#38b2ac" stroke-width="2"/>
                    <text x="700" y="260" text-anchor="middle" font-size="14" fill="#2d3748">梅斯 (Metz)</text>
                    <text x="700" y="280" text-anchor="middle" font-size="12" fill="#4a5568">符码系统</text>
                    
                    <!-- 文本分析 -->
                    <line x1="400" y1="320" x2="400" y2="350" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                    <rect x="200" y="350" width="400" height="50" rx="5" ry="5" fill="#fff" stroke="#4a5568" stroke-width="2"/>
                    <text x="400" y="380" text-anchor="middle" font-size="18" fill="#2d3748">文本分析 (analyse textuelle)</text>
                </svg>
            </div>
            
            <div class="concept-box">
                <h3>核心概念</h3>
                <ul>
                    <li><span class="concept">深层结构</span>：隐藏在意义生产过程中，能够解释外在表现形式的内在结构</li>
                    <li><span class="concept">二元对立系统</span>：通过对立和差异形成意义的系统（例如索绪尔提出的语言与言语之间的区别）</li>
                    <li><span class="concept">符码</span>：一种由关系与区别形成的系统，可以描述电影语言活动内部的多重表意功能</li>
                </ul>
            </div>
            
            <div class="theorist">
                <h3>主要理论家及贡献</h3>
                <ul>
                    <li><strong>列维·斯特劳斯</strong>：通过研究神话发现深层结构，推论表面上极其不同的意义生产活动可能共享相同的结构</li>
                    <li><strong>艾柯</strong>：在《不在的结构》中提出意义与沟通现象组成符号系统的观念，详细阐述了影像符码定义</li>
                    <li><strong>罗兰·巴特</strong>：分析社会流传产品中的意识形态表现，探究"内涵意旨"在影像内在意义网络中的地位</li>
                    <li><strong>梅斯</strong>：在《语言活动与电影》中系统定义了符码概念，涵盖所有影片表意作用的规律及系统化现象</li>
                </ul>
            </div>
        </div>
        
        <div class="knowledge-section">
            <h2>二、影片文本的概念演变</h2>
            
            <div class="concept-box">
                <h3>三个基本概念</h3>
                <ol>
                    <li><span class="concept">影片文本</span> (le texte filmique)：作为"言谈齐一性之实际体现"的具体影片，即电影语言符码的具体组合与运用</li>
                    <li><span class="concept">影片文本系统</span> (le systeme textuel filmique)：每部影片所独有的结构模式，由分析家建构，是一种接受该文本固有逻辑性与紧密性制约的特定符码组合方式</li>
                    <li><span class="concept">符码</span> (le code)：本身也是一种由关系与区别形成的系统，但与影片文本系统不同，符码是一种可运用在不同文本上、更具普遍性的系统</li>
                </ol>
            </div>
            
            <div class="timeline">
                <h3>文本概念的演变历程</h3>
                
                <div class="timeline-item">
                    <h4>茱莉亚·克丽斯特娃</h4>
                    <p>在《Tel Quel》上提出文本定义，认为文本不是陈列在书店里的作品，而是笔体(ecriture)本身的"空间"(espace)。文本被视为意义生产的（无尽的）过程，潜在着无尽且无数的阅读空间活动。</p>
                </div>
                
                <div class="timeline-item">
                    <h4>罗兰·巴特的《S/Z》(1970)</h4>
                    <p>提出了理论折中做法。将文本"抄体"(scriptible)视为作品完结的否定，以一个更具运作性的作品"复数性"("pluriel" d'une oeuvre)取代，即文学不是由抄写的文本组成，而是由阅读的(lisible)作品组成。</p>
                    <p>关键贡献：提出了<span class="concept">多义性</span>(polysémie)概念和<span class="concept">内涵意指</span>(connotation)作为分析工具。</p>
                </div>
                
                <div class="timeline-item">
                    <h4>实际分析方法</h4>
                    <p>巴特引入<span class="concept">词组</span>(lexie)概念，一个由分析家自行规划的、长短不拘的文本片段。整个分析过程按顺序检视这些词组，指出内涵意指的能指单元，再将每个内涵意指归属到五个一般符码系统中。</p>
                </div>
            </div>
            
            <div class="example">
                <h4>蒂埃里·昆塞尔对弗里茨·朗的《M》的分析</h4>
                <p>这是早期典范。昆塞尔完全采用巴特的分析过程——把影片文本按事件发展顺序切分成词组，不理会组合段或叙事空间的"技术性"间歇，将片头分为三个长度不等的词组：影片字幕部分、第一个镜头、该片段其余全部镜头。</p>
                <p>这篇论文几乎是最早"运用"文本分析概念分析影片的理想表征，明确显示文本分析绝非单纯"运用"一个"模式"就算了事。</p>
            </div>
        </div>
        
        <div class="knowledge-section">
            <h2>三、影片符码分析</h2>
            
            <div class="concept-box">
                <h3>符码概念面临的问题</h3>
                <ol>
                    <li>不是所有符码都具有同等重要性，各个符码概念在普遍性方面呈现异质化现象</li>
                    <li>符码从不以"纯粹"状态出现，因为影片既是符码具体体现的成果，也是符码系统支配规则形成的所在</li>
                    <li>当衡量影片艺术内在特质时，运用符码概念多少显得力不从心，尤其面对具有原创性的影片</li>
                </ol>
            </div>
            
            <div class="diagram">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- 分段方法标题 -->
                    <rect x="200" y="20" width="300" height="50" rx="5" ry="5" fill="#edf2f7" stroke="#4a5568" stroke-width="2"/>
                    <text x="350" y="50" text-anchor="middle" font-size="18" fill="#2d3748">影片分段分析方法</text>
                    
                    <!-- 三种分段方法 -->
                    <rect x="50" y="120" width="180" height="120" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                    <text x="140" y="150" text-anchor="middle" font-size="16" fill="#2d3748">大组合段</text>
                    <text x="140" y="175" text-anchor="middle" font-size="14" fill="#4a5568">梅斯分析《再见菲律宾》</text>
                    <text x="140" y="200" text-anchor="middle" font-size="14" fill="#4a5568">七种符码类型</text>
                    
                    <rect x="260" y="120" width="180" height="120" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                    <text x="350" y="150" text-anchor="middle" font-size="16" fill="#2d3748">超段落</text>
                    <text x="350" y="175" text-anchor="middle" font-size="14" fill="#4a5568">与剧情叙事相对应</text>
                    <text x="350" y="200" text-anchor="middle" font-size="14" fill="#4a5568">贝卢尔提出</text>
                    
                    <rect x="470" y="120" width="180" height="120" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                    <text x="560" y="150" text-anchor="middle" font-size="16" fill="#2d3748">次段落</text>
                    <text x="560" y="175" text-anchor="middle" font-size="14" fill="#4a5568">由于"轻微的"情节改变</text>
                    <text x="560" y="200" text-anchor="middle" font-size="14" fill="#4a5568">而进行的细分</text>
                    
                    <!-- 连接线 -->
                    <line x1="350" y1="70" x2="350" y2="90" stroke="#4a5568" stroke-width="2"/>
                    <line x1="350" y1="90" x2="140" y2="120" stroke="#4a5568" stroke-width="2"/>
                    <line x1="350" y1="90" x2="350" y2="120" stroke="#4a5568" stroke-width="2"/>
                    <line x1="350" y1="90" x2="560" y2="120" stroke="#4a5568" stroke-width="2"/>
                    
                    <!-- 风格分析 -->
                    <rect x="200" y="260" width="300" height="40" rx="5" ry="5" fill="#fff" stroke="#4a5568" stroke-width="2"/>
                    <text x="350" y="285" text-anchor="middle" font-size="16" fill="#2d3748">分段作为影片风格特性的描述工具</text>
                    
                    <line x1="140" y1="240" x2="140" y2="280" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="140" y1="280" x2="200" y2="280" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="350" y1="240" x2="350" y2="260" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="560" y1="240" x2="560" y2="280" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="560" y1="280" x2="500" y2="280" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                </svg>
            </div>
            
            <div class="grid-container">
                <div class="grid-item">
                    <h4>梅斯的符码分析</h4>
                    <p>将《再见菲律宾》完整分段，把每个段落归入大组合段界定的七种符码类型之一，标出段落间的标点符号与界限。</p>
                    <p>结论：大组合段并非"绝对的"符码，它只是这个语言系统在理论及实务发展过程中的一个历史阶段。</p>
                </div>
                
                <div class="grid-item">
                    <h4>贝卢尔的分段方法</h4>
                    <p>对《金粉世界》所作的分析，建议同时考虑使用与剧情叙事相对应的"超段落"单元，以及同一段落中由于"轻微的"情节改变而再加区分的"次段落"单元。</p>
                    <p>这种分段方式具有重要理论价值，几乎所有叙事电影都可运用这种分段方式。</p>
                </div>
                
                <div class="grid-item">
                    <h4>米歇尔·马利的声音分析</h4>
                    <p>对阿兰·雷奈的《穆里爱》所作分析，特别关注声音符码问题。两个关键发现：声音分析轴线不是独立自主的，"声音符码"具有复数形式。</p>
                </div>
            </div>
        </div>
        
        <div class="knowledge-section">
            <h2>四、完尽的分析与无尽的分析</h2>
            
            <div class="concept-box">
                <h3>完尽分析的幻影</h3>
                <p>文本分析的中心问题是分析本身是否完备，是否称得上是意义焕然彰显的论文。这种对文本做出详尽而齐全分析的想法，一直被当作乌托邦幻影——一件可以想象但不可能在现实中出现的事。</p>
                <p>从更积极的角度看，它如同分析的地平线，我们向前进时，它就会往后退得更远。这意味着分析永远无法完尽。</p>
            </div>
            
            <div class="grid-container">
                <div class="grid-item">
                    <h4>片段分析的价值</h4>
                    <ul>
                        <li>能以精确的方式对细节进行深入分析</li>
                        <li>作为整体分析的代替品，通过部分获得整体分析效果</li>
                        <li>保持电影分析所需的严谨精确度</li>
                    </ul>
                </div>
                
                <div class="grid-item">
                    <h4>片段选择标准</h4>
                    <ol>
                        <li>选择作为分析对象的影片片段必须是一个完整片段</li>
                        <li>这段影片必须在结构上紧密扎实，具有明显内在组织系统</li>
                        <li>这段影片必须足以代表整部影片，具有"代表性"</li>
                    </ol>
                </div>
                
                <div class="grid-item">
                    <h4>片头分析的特殊价值</h4>
                    <p>把影片片头视为影片"<span class="concept">矩阵</span>"(matrice)模具的分析方式具有生产性。片头决定了每部片子或每个类型本身虚构状态以及观众入戏状态。</p>
                    <p>例如：蒂埃里·昆塞尔分析《危险游戏》时，指出字幕部分就以影片矩阵形式呈现，形成所有叙事段落及影片表现的矩阵。</p>
                </div>
            </div>
            
            <div class="note">
                <h4>文本分析的本质</h4>
                <p>影片分析从电影结构主义符号学开始，经历了从强调符码系统到探讨文本多义性的演变。文本分析最根本的特性是认识到任何分析都无法完尽，同时也是理解电影作为特殊文本形式的重要途径。</p>
                <p>无论是通过大组合段、片段分析还是片头作为矩阵的研究，电影文本分析为我们提供了理解电影艺术复杂性的重要工具，使我们能够超越表面叙事，深入探索电影的深层结构和多重意义。</p>
            </div>
        </div>
    </div>
</body>
</html> 