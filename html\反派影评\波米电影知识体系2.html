<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>波米电影知识体系</title>
    <style>
        :root {
            --primary-color: #3a0ca3;
            --secondary-color: #4361ee;
            --accent-color: #f72585;
            --text-color: #2b2d42;
            --light-bg: #f8f9fa;
            --dark-bg: #212529;
        }
        
        body {
            font-family: "Noto Sans SC", "PingFang SC", "Microsoft YaHei", sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 40px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        header p {
            margin-top: 10px;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        nav {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 10px;
            z-index: 100;
        }
        
        nav ul {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        nav a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        nav a:hover {
            background-color: var(--light-bg);
            color: var(--accent-color);
        }
        
        main {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        section {
            margin-bottom: 40px;
        }
        
        h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 10px;
            margin-top: 40px;
        }
        
        h3 {
            color: var(--secondary-color);
            margin-top: 25px;
        }
        
        p {
            margin-bottom: 16px;
        }
        
        .quote {
            background-color: var(--light-bg);
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin: 20px 0;
            font-style: italic;
        }
        
        .movie-card {
            background-color: var(--light-bg);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }
        
        .movie-card h4 {
            color: var(--accent-color);
            margin-top: 0;
        }
        
        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        
        /* SVG 容器样式 */
        .svg-container {
            width: 100%;
            margin: 30px 0;
            text-align: center;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            header {
                padding: 20px;
            }
            
            header h1 {
                font-size: 2em;
            }
            
            nav ul {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
    <!-- MathJax支持 -->
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
</head>
<body>
    <header>
        <h1>波米电影知识体系</h1>
        <p>基于波米分答整理的电影艺术与欣赏知识系统</p>
    </header>
    
    <nav>
        <ul>
            <li><a href="#intro">引言</a></li>
            <li><a href="#directors">导演与创作者</a></li>
            <li><a href="#genres">电影类型与流派</a></li>
            <li><a href="#techniques">电影技术与美学</a></li>
            <li><a href="#history">电影历史与文化</a></li>
            <li><a href="#appreciation">电影欣赏与评论</a></li>
        </ul>
    </nav>
    
    <main>
        <section id="intro">
            <h2>引言：电影艺术的多维认知</h2>
            <p>电影作为第七艺术，结合了视觉、听觉、叙事等多种艺术形式，创造出独特的艺术体验。本知识体系基于波米的电影分析和见解，旨在提供一个系统化的电影知识框架，帮助读者从多角度理解和欣赏电影艺术。</p>
            
            <div class="quote">
                "电影是第七艺术，它永远是艺术，电影的艺术属性非常强...而电视剧在我看来，它最强的第一属性其实就是一个娱乐商品，他是为了给最大多数的人茶余饭后消遣的，就跟嗑瓜子的那个瓜子一样，它是一个精神瓜子。"
            </div>
        </section>
        
        <!-- 后续内容将在这里添加 -->
        
    </main>
    
    <footer>
        <p>© 2023 波米电影知识体系 | 基于波米分答内容整理</p>
    </footer>
</body>
</html> 