.container {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

h1 {
    color: #2c5aa0;
    text-align: center;
    border-bottom: 3px solid #e74c3c;
    padding-bottom: 20px;
    margin-bottom: 30px;
    font-size: 2.2em;
}

h2 {
    color: #e74c3c;
    border-left: 5px solid #3498db;
    padding-left: 15px;
    margin-top: 40px;
    margin-bottom: 20px;
    font-size: 1.6em;
}

h3 {
    color: #8e44ad;
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.section-divider {
    height: 2px;
    background: linear-gradient(to right, #3498db, #e74c3c, #3498db);
    margin: 30px 0;
    border-radius: 1px;
}

.highlight-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.concept-box {
    background: #f8f9fa;
    border-left: 4px solid #28a745;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 8px 8px 0;
}

.quote-box {
    background: #fff9e6;
    border: 1px solid #ffd700;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    font-style: italic;
    position: relative;
}

.quote-box::before {
    content: '"';
    font-size: 3em;
    color: #ffd700;
    position: absolute;
    top: -10px;
    left: 10px;
}

.theory-chart {
    margin: 30px 0;
    text-align: center;
}

.flowchart {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 20px 0;
    flex-wrap: wrap;
}

.flowchart-item {
    background: #3498db;
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin: 10px;
    min-width: 150px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.arrow {
    font-size: 2em;
    color: #e74c3c;
    margin: 0 10px;
}

.method-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.method-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    transition: transform 0.3s ease;
}

.method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.emphasis {
    color: #e74c3c;
    font-weight: bold;
}

.code-concept {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 3px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.analysis-structure {
    background: #ecf0f1;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.toc {
    background: #e8f4f8;
    padding: 20px;
    border-radius: 10px;
    margin: 30px 0;
    border-left: 5px solid #3498db;
}

.toc ul {
    margin: 0;
    padding-left: 20px;
}

.toc li {
    margin: 8px 0;
}

.toc a {
    color: #2c5aa0;
    text-decoration: none;
}

.toc a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .container {
        padding: 20px;
    }
    
    .flowchart {
        flex-direction: column;
    }
    
    .arrow {
        transform: rotate(90deg);
    }
    
    .method-grid {
        grid-template-columns: 1fr;
    }
}
</style>
<div class="toc">
    <h3>目录</h3>
    <ul>
        <li><a href="#intro">引言</a></li>
        <li><a href="#structuralism">文本分析与结构主义</a></li>
        <li><a href="#film-text">影片文本概念</a></li>
        <li><a href="#code-analysis">影片符码分析</a></li>
        <li><a href="#practice">分析实践与方法</a></li>
        <li><a href="#controversy">文本分析的争议性</a></li>
    </ul>
</div>

<div class="section-divider"></div>

<div id="intro" class="highlight-box">
    <h2>引言</h2>
    <p>文本分析（analyse textuelle）的产生，主要是为了解决影片分析中客体选择的<span class="emphasis">散漫性</span>和方法选择的<span class="emphasis">不确定性</span>问题。这一方法在电影理论中占据重要地位，不是因为它在本质上有根本不同，而是基于两个明确原因：</p>
    <ul>
        <li><span class="emphasis">"文本"概念</span>对影片及影片分析的统一性提出了最根本的问题</li>
        <li><span class="emphasis">文本分析</span>曾经几乎变成影片分析的一般代名词</li>
    </ul>
</div>

<div class="section-divider"></div>

<h2 id="structuralism">一、文本分析与结构主义</h2>

<h3>1. 基本概念</h3>

<div class="concept-box">
    <p><strong>结构主义的核心思想</strong>：结构主义分析家试图辨明隐蔽在意义生产过程中、足以解释其外现形式的<span class="code-concept">"深层"结构</span>。</p>
</div>

<div class="theory-chart">
    <h4>列维-斯特劳斯的神话分析模式</h4>
    <div class="flowchart">
        <div class="flowchart-item">表面复杂的神话叙述</div>
        <div class="arrow">→</div>
        <div class="flowchart-item">深层结构的规律性</div>
        <div class="arrow">→</div>
        <div class="flowchart-item">系统性特质</div>
    </div>
</div>

<div class="quote-box">
    "不论在思辨层面或是实践层面，差别的明显性较其内容更重要得多：只要它存在，就会形成一种有用的区别系统，如同一个可以运用在一个初窥乍看之下完整而难以理解的文本之上的架构。"
    <br><em>—— 列维-斯特劳斯《野性的思维》</em>
</div>

<h3>2. 结构主义在电影分析中的应用</h3>

<div class="method-grid">
    <div class="method-card">
        <h4>列维-斯特劳斯式分析</h4>
        <p>杜蒙和莫诺对库布里克《2001年：太空漫游》的分析，作为"新版"星宿神话来剖析影片，注重声带部分的分析。</p>
    </div>
    
    <div class="method-card">
        <h4>艾柯的符号系统</h4>
        <p>在《不在的结构》中提出意义与沟通现象组成符号系统的观念，通过符码（codes）与讯息（message）的关系进行研究。</p>
    </div>
    
    <div class="method-card">
        <h4>巴特的影像修辞</h4>
        <p>分析广告影像，侧重意义层面，探勘内涵意旨（connotation）在影像内在意义网络中的地位。</p>
    </div>
</div>

<div class="section-divider"></div>

<h2 id="film-text">二、影片文本概念</h2>

<h3>1. 文本概念的演变</h3>

<div class="analysis-structure">
    <h4>电影结构主义符号学的三个基本概念：</h4>
    <ol>
        <li><strong>影片文本（le texte filmique）</strong>：作为"言谈齐一性之实际体现"的具体影片</li>
        <li><strong>影片文本系统（le système textuel filmique）</strong>：每部影片所独有的结构模式</li>
        <li><strong>符码（le code）</strong>：可以运用在不同文本上、更具普遍性的系统</li>
    </ol>
</div>

<div class="concept-box">
    <h4>克丽斯特娃的文本定义</h4>
    <p>文本不是陈列在书店里的作品，而是<span class="emphasis">笔体（écriture）本身的"空间"</span>。文本被视为意义生产的无尽过程，潜在着无尽且无数的阅读空间活动。</p>
</div>

<h3>2. 巴特《S/Z》的分析模式</h3>

<div class="theory-chart">
    <h4>巴特的五大符码系统</h4>
    <svg width="600" height="300" style="margin: 20px auto; display: block;">
        <rect x="50" y="50" width="100" height="60" fill="#3498db" rx="10"/>
        <text x="100" y="85" text-anchor="middle" fill="white" font-size="12">指涉性符码</text>
        
        <rect x="200" y="50" width="100" height="60" fill="#e74c3c" rx="10"/>
        <text x="250" y="85" text-anchor="middle" fill="white" font-size="12">象征性符码</text>
        
        <rect x="350" y="50" width="100" height="60" fill="#f39c12" rx="10"/>
        <text x="400" y="85" text-anchor="middle" fill="white" font-size="12">语义素符码</text>
        
        <rect x="125" y="150" width="100" height="60" fill="#27ae60" rx="10"/>
        <text x="175" y="185" text-anchor="middle" fill="white" font-size="12">诠释符码</text>
        
        <rect x="275" y="150" width="100" height="60" fill="#8e44ad" rx="10"/>
        <text x="325" y="185" text-anchor="middle" fill="white" font-size="12">行动结果确立符码</text>
    </svg>
</div>

<div class="quote-box">
    巴特的分析方法：将文本依照先后次序检视词组（lexie），指出其内涵意指的能指单元，再将每一个内涵意指归属到五个一般符码系统中。这种"容量分析"式的阅读方式打破了作品的常态性。
</div>

<div class="section-divider"></div>

<h2 id="code-analysis">三、影片符码分析</h2>

<h3>1. 符码概念的实质意义</h3>

<div class="highlight-box">
    <h4>符码系统分析的三个主要问题：</h4>
    <ol>
        <li><span class="emphasis">重要性不对等</span>：各个符码概念在普遍性方面呈现异质化现象</li>
        <li><span class="emphasis">共生运作</span>：符码从来不以"纯粹"状态出现，影片既运用符码也创造符码</li>
        <li><span class="emphasis">原创性挑战</span>：符码分析比较适用于系列式影片或"一般"电影，对伟大影片的原创性分析显得力不从心</li>
    </ol>
</div>

<h3>2. 大组合段模式的应用</h3>

<div class="method-card">
    <h4>梅斯对《再见菲律宾》的分析</h4>
    <p>通过完整的影片分段，将每个段落归到七种符码类型中的一种，并从组合段出现频率描绘影片的风格特性：</p>
    <ul>
        <li>典型的"新电影"风格</li>
        <li>形式的解放</li>
        <li>影片叙事的"简单化"、"透明化"趋向</li>
        <li>"戈达尔/直接电影"趋势</li>
    </ul>
</div>

<div class="section-divider"></div>

<h2 id="practice">四、分析实践与方法</h2>

<h3>1. 完尽分析的幻影</h3>

<div class="concept-box">
    <p><strong>核心问题</strong>：文本分析的中心问题是分析本身是否完备，是否能成为意义焕然彰显的论文。对文本做出详尽而齐全的分析被视为一种<span class="emphasis">乌托邦幻影</span>——可以想象但不可能在现实中实现。</p>
</div>

<h3>2. 影片片段分析</h3>

<div class="analysis-structure">
    <h4>片段选择的三个标准：</h4>
    <ol>
        <li><strong>完整性</strong>：必须是一个完整的片段</li>
        <li><strong>结构性</strong>：在结构上紧密扎实，具有明显的内在组织系统</li>
        <li><strong>代表性</strong>：必须足以代表整部影片</li>
    </ol>
</div>

<div class="method-grid">
    <div class="method-card">
        <h4>昆塞尔的《M》片头分析</h4>
        <p>采用巴特的分析过程，将影片依事件发展切分成词组，界定每个词组的功能，运用多种符码系统。</p>
    </div>
    
    <div class="method-card">
        <h4>贝卢尔的《夜长梦多》分析</h4>
        <p>选择简单段落（12个镜头），分析正/反拍镜头的剪接逻辑，探讨编码化好莱坞电影的"明显性"外衣。</p>
    </div>
</div>

<h3>3. 影片开场分析的特殊意义</h3>

<div class="quote-box">
    影片片头作为影片"矩阵"（matrice）的分析方式：片头不是将文本禁锢在固定系统中，而是具有起动力量，强化发展与转变的可能性，因为它建立在矛盾原则之上，包含了否定自己的原基。
</div>

<div class="section-divider"></div>

<h2 id="controversy">五、文本分析的争议性</h2>

<h3>主要批评论点</h3>

<div class="method-grid">
    <div class="method-card" style="border-left: 4px solid #e74c3c;">
        <h4>1. 适用范围限制</h4>
        <p>文本分析主要适用于叙事电影，特别是传统叙事电影，对实验电影等其他类型电影的适用性受到质疑。</p>
    </div>
    
    <div class="method-card" style="border-left: 4px solid #f39c12;">
        <h4>2. 为分析而分析</h4>
        <p>容易助长研究者"剖析力比多"（libido decorticandi）的冲动，以单镜头"接香肠"式分镜表替代真正的分析。</p>
    </div>
    
    <div class="method-card" style="border-left: 4px solid #3498db;">
        <h4>3. 忽视语境</h4>
        <p>过分忽视被研究影片的语境，包括制片过程与观众反应等背景因素。</p>
    </div>
    
    <div class="method-card" style="border-left: 4px solid #8e44ad;">
        <h4>4. 简约化危险</h4>
        <p>具有将影片简约成系统框架的危险性，可能"谋杀"影片，使影片僵化成木乃伊。</p>
    </div>
</div>

<div class="highlight-box">
    <h3>文本分析的积极意义</h3>
    <p>尽管存在批评，文本分析体现了<span class="emphasis">影片是由电影内在或外在表意系统网络所构成</span>的基本概念。它不仅与"纯粹的"影片或电影现象有关，更与象征（symbolique）相关。文本分析为后续的精神分析、"解构"分析等研究方法开辟了道路，至今仍是电影分析的重要方法论支柱。</p>
</div>

<div class="section-divider"></div>

<div class="concept-box">
    <h3>结论</h3>
    <p>文本分析作为电影理论与实践的重要方法，虽然面临各种批评和争议，但其对电影表意系统的深入探索和对分析方法的理论化贡献是不可否认的。关键在于避免机械化应用，保持分析的开放性和探索精神，使其真正服务于对电影艺术本质的理解。</p>
</div>
</div>