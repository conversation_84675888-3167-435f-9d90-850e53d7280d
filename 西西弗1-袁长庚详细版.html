<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话袁长庚："一切青年问题都是历史债务问题"</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimSun', serif;
            line-height: 1.8;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.8em;
            margin-bottom: 30px;
            border-bottom: 4px solid #e74c3c;
            padding-bottom: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        h2 {
            color: #e74c3c;
            font-size: 1.9em;
            margin-top: 45px;
            margin-bottom: 25px;
            padding: 15px 20px;
            border-left: 6px solid #e74c3c;
            background: linear-gradient(90deg, #ffe6e6 0%, transparent 100%);
            border-radius: 0 10px 10px 0;
        }
        
        h3 {
            color: #8e44ad;
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 18px;
            padding-left: 10px;
            border-left: 3px solid #8e44ad;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 4px 10px;
            border-radius: 6px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .quote {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-left: 5px solid #fff;
            padding: 20px 25px;
            margin: 25px 0;
            font-style: italic;
            border-radius: 0 15px 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .analysis {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .key-point {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: 2px solid #ff9a56;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(255, 154, 86, 0.2);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 25px 0;
        }
        
        .comparison-item {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .past {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border: 2px solid #4ecdc4;
        }
        
        .present {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border: 2px solid #ff6b6b;
        }
        
        .flow-chart {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .flow-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 18px;
            border-radius: 12px;
            margin: 12px;
            min-width: 160px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .arrow {
            font-size: 2.5em;
            color: #e74c3c;
            margin: 0 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .detailed-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #17a2b8;
        }
        
        .speaker-quote {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
            font-style: italic;
        }
        
        .warning {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff4757;
        }
        
        .timeline {
            position: relative;
            margin: 30px 0;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 25px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e74c3c;
            border: 3px solid white;
        }
        
        .conclusion {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            padding: 30px;
            border-radius: 20px;
            margin-top: 50px;
            text-align: center;
            font-size: 1.15em;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        ul, ol {
            padding-left: 35px;
        }
        
        li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .flow-chart {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>对话袁长庚："一切青年问题都是历史债务问题"</h1>
        
        <div class="quote">
            本文基于与云南大学社会学副教授袁长庚的深度对话，探讨当代中国青年问题的新变化、制度根源以及历史债务理论，揭示了从"发疯"到"尸体"的话语转变背后的深层社会逻辑。
        </div>

        <h2>一、年轻人问题的新阶段：从主动求助到被动退却</h2>
        
        <p>今年关于年轻人，大家问的问题和去年有什么变化吗？<span class="highlight">这一年没有人再问我这个问题，大家已经默认这个问题已经充分出现了</span>，好像就默认年轻人或者这一届年轻人，他们未来几年的轨迹差不多是固定的。</p>
        
        <div class="speaker-quote">
            "而且我自己的感觉是年轻人在退却，就是像我这个年龄的人，他们不太有兴趣再跟你主动说什么。我大概刚工作的时候，年轻人还是挺迫切的想跟你说一些事情，或者说想从你这儿得到什么答案之类的。"
        </div>
        
        <h3>师生关系的分离现象</h3>
        <p>但是这一两年给我的感觉就是说好像他们也不问了。因为对你的答案有预期，然后对你的态度有预期，所以他就觉得反而问了我们也解决不了什么问题，所以我们就自己闷起头来去该干什么干什么就可以了。</p>
        
        <div class="key-point">
            <strong>核心观察：</strong>在大学里，本来老师跟学生之间是一个接触最密切的群体。但是现在观察的时候，这两个群体是在分离的。这两个群体的接触其实很能说明问题——我们现在这个社会到底所谓年轻人问题到了一个什么样的阶段。
        </div>
