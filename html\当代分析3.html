<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第三章 文本分析 -- 一个引人争议的模式</title>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, 'Microsoft Yahei', sans-serif;
            line-height: 1.8;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            padding: 25px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
        }
        h1 {
            text-align: center;
            font-size: 2.2em;
            color: #1a5276; /* 强调色 */
            border-bottom: 2px solid #1a5276;
            padding-bottom: 10px;
        }
        h2 {
            font-size: 1.8em;
            color: #1f618d; /* 强调色 */
            border-left: 4px solid #1f618d;
            padding-left: 10px;
        }
        h3 {
            font-size: 1.5em;
            color: #2471a3; /* 强调色 */
        }
        h4 {
            font-size: 1.2em;
            color: #2980b9; /* 强调色 */
        }
        p {
            margin-bottom: 1em;
            text-align: justify;
        }
        .intro, .conclusion {
            font-style: italic;
            color: #555;
            background-color: #eaf2f8;
            padding: 15px;
            border-left: 5px solid #2980b9;
            border-radius: 4px;
        }
        .key-concept {
            font-weight: bold;
            color: #c0392b; /* 强调色 */
        }
        .theorist {
            font-style: italic;
            color: #27ae60; /* 强调色 */
        }
        .work-title {
            font-style: italic;
        }
        blockquote {
            margin: 1em 20px;
            padding: 1em 15px;
            background-color: #f4f6f7;
            border-left: 5px solid #bdc3c7;
            color: #566573;
        }
        ul {
            list-style-type: square;
            padding-left: 20px;
        }
        li {
            margin-bottom: 0.5em;
        }
        .figure-container {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border: 1px dashed #ccc;
            border-radius: 5px;
            background-color: #fdfefe;
        }
        .figure-caption {
            font-size: 0.9em;
            color: #777;
            margin-top: 5px;
        }
        .references h3 {
            color: #1a5276;
            border-bottom: 1px solid #1a5276;
            padding-bottom: 5px;
        }
        .references ul {
            list-style-type: none;
            padding-left: 0;
        }
        .references li {
            margin-bottom: 0.8em;
            font-size: 0.95em;
        }
        .emphasis-box {
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #aed6f1;
            background-color: #e PBF5FB;
            border-radius: 5px;
        }
        .emphasis-box h4 {
            margin-top: 0;
            color: #1f618d;
        }

        /* SVG specific styles */
        .svg-diagram {
            display: block;
            margin: auto;
            max-width: 100%;
            height: auto;
        }
        .svg-text {
            font-family: 'Helvetica Neue', Helvetica, Arial, 'Microsoft Yahei', sans-serif;
            font-size: 14px;
        }
        .svg-box {
            stroke: #2980b9;
            stroke-width: 1.5;
            fill: #eaf2f8;
        }
        .svg-arrow {
            stroke: #2c3e50;
            stroke-width: 1.5;
            fill: none;
            marker-end: url(#arrowhead);
        }
        .svg-line {
            stroke: #7f8c8d;
            stroke-width: 1;
            stroke-dasharray: 4, 2;
        }
        .svg-highlight-text {
            fill: #c0392b;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>第三章 文本分析 —— 一个引人争议的模式</h1>

        <p class="intro">
            本章探讨“文本分析”（analyse textuelle）这一影片分析方法。它并非本质上彻底不同或能解决所有困难，而是因其对影片及影片分析统一性提出的根本问题（“文本”概念）以及曾几乎成为影片分析代名词的地位而占据重要篇幅。本章旨在澄清相关概念与争议。
        </p>

        <section id="textual-analysis-and-structuralism">
            <h2>一、文本分析与结构主义</h2>
            <p>文本分析的产生与发展深受结构主义（structuralisme）思潮影响。结构主义在20世纪60年代广泛应用于各种思想著作，电影理论与影片分析尤为显著。</p>

            <div class="figure-container">
                <svg width="500" height="200" viewBox="0 0 500 200" class="svg-diagram">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                        </marker>
                    </defs>
                    
                    <rect x="20" y="70" width="200" height="60" rx="5" ry="5" class="svg-box"/>
                    <text x="120" y="105" text-anchor="middle" class="svg-text">表面任意复杂的叙述/现象</text>
                    <text x="120" y="85" text-anchor="middle" class="svg-text">(例如：神话、文本)</text>
                    
                    <rect x="280" y="70" width="200" height="60" rx="5" ry="5" class="svg-box"/>
                    <text x="380" y="105" text-anchor="middle" class="svg-text"><tspan class="svg-highlight-text">深层结构 (Structure Profonde)</tspan></text>
                    <text x="380" y="85" text-anchor="middle" class="svg-text">(规律性、系统性)</text>

                    <line x1="225" y1="100" x2="275" y2="100" class="svg-arrow" />
                    <text x="250" y="90" text-anchor="middle" class="svg-text">结构主义分析揭示</text>
                </svg>
                <p class="figure-caption">图1：结构主义核心思想示意图——从表面现象探寻深层结构。</p>
            </div>

            <h3>1. 基本概念</h3>
            <p>结构主义的核心是<span class="key-concept">“结构”</span>概念。分析家试图辨明隐藏在意义生产过程中，足以解释其外现形式的<span class="key-concept">“深层结构”</span>（structure "profonde"）。</p>
            <ul>
                <li><span class="theorist">列维·斯特劳斯</span>（Claude Lévi-Strauss）通过研究神话，发现其表面任意复杂的内容下具有强烈的规律性与系统性，即神话的“深层结构”。他认为，表面相异的意义生产活动可共享同一结构。他强调“差别的明显性较其内容更重要得多”，差别系统（如文本分析中的切分和对比）是解读文本的关键。</li>
                <li><span class="key-concept">“结构”语言学</span>是结构主义的重要参考坐标。语言被视为所有意义生产过程的基底架构，结构常被视为<span class="key-concept">二元对立系统</span>（oppositions binaires），源于<span class="theorist">索绪尔</span>（Ferdinand de Saussure）的语言（langue）与言语（parole）之分。</li>
                <li><span class="theorist">列维·斯特劳斯</span>致力于建立“神话素”（mythemes）体系，类比语言学中的“词素”（morphemes）。</li>
                <li><span class="theorist">拉康</span>（Jacques Lacan）宣称“无意识（inconscient）有如语言活动一般的结构”。</li>
            </ul>

            <h3>2. 结构主义分析</h3>
            <p>结构主义分析可应用于神话、无意识、文学艺术创作（如电影）等意义生产活动。电影的文本分析衍生自结构主义分析。</p>
            <ul>
                <li><span class="theorist">列维·斯特劳斯</span>本人较少实践于文学艺术作品，曾与<span class="theorist">罗曼·雅各布森</span>（Roman Jakobson）合作分析波特莱尔的诗作《猫》（Les Chats）。</li>
                <li><span class="theorist">让-保罗·杜蒙</span>与<span class="theorist">让·莫诺</span>对库布里克《2001太空漫游》的分析是最彻底的“列维·斯特劳斯式”影片分析，侧重厘清影片的“语意结构”，强调“从方法论上否认最终意义的存在”，关注能指间的关系。</li>
                <li><span class="theorist">艾柯</span>（Umberto Eco）在《不在的结构》中提出意义与沟通现象组成<span class="key-concept">符号系统</span>（systèmes de signe）的观念，通过将个别讯息与一般<span class="key-concept">符码</span>（codes）联系进行研究，并阐明影像符码定义。</li>
                <li><span class="theorist">罗兰·巴特</span>（Roland Barthes）对文本分析影响深远。
                    <ul>
                        <li>《影像的修辞》：分析广告影像（庞札尼面条），侧重意义层面，探勘“内涵意旨”（connotation），关注影像内在意义网络。</li>
                        <li>《神话学》：分析各种“文本”内部的意识形态表现，指陈社会流传产品（含电影）承载系统化意涵，隶属符号学研究。</li>
                        <li>1960年已提出电影结构主义分析大原则，质疑影片内涵意义的位置、形式与效果，以及能指与所指的关系。</li>
                    </ul>
                </li>
                <li><span class="theorist">梅斯</span>（Christian Metz）在《语言活动与电影》中系统化回答巴特提问，影响电影分析理论与实务。其核心概念是<span class="key-concept">“符码”</span>（code）：
                    <ul>
                        <li>涵括所有影片表意作用的规律及系统化现象，替代电影的“语言”（langue）。</li>
                        <li>符码借组合关系形成，理论上可独立，实际以共生方式运作。</li>
                        <li>描述电影语言活动内部的多重表意功能，不能像语言一样承载直接外延意义。</li>
                        <li>可检视特定影片与电影共通的普遍现象（如具象类比关系）或局部现象（如背景投影法），并界定影片外围文化条件（如类型电影）。</li>
                    </ul>
                </li>
            </ul>
        </section>

        <section id="film-text">
            <h2>二、影片文本</h2>
            <p>影片分析从电影结构主义符号学中撷取了三个基本概念：</p>
            <ol>
                <li><span class="key-concept">影片文本</span>（le texte filmique）：作为“言谈齐一性之实际体现”的具体影片，即电影语言符码的具体组合、运用。</li>
                <li><span class="key-concept">影片文本系统</span>（le système textuel filmique）：每部影片所独有的结构模式，由分析家建构，是特定符码组合方式。</li>
                <li><span class="key-concept">符码</span>（le code）：一种可运用于不同文本的、更具普遍性的系统（文本成为符码的“信息”）。</li>
            </ol>

            <h3>1. 文本概念的演变</h3>
            <ul>
                <li>20世纪60年代末，文本也特指现代（文学范畴的）文本。</li>
                <li><span class="theorist">茱莉亚·克丽斯特娃</span>（Julia Kristeva）认为文本是笔体（écriture）本身的“空间”，是意义生产的（无尽）过程，潜在无数阅读空间活动，构成现代文学文本生产力（productivité）。此定义未立即被影片学接受，因其狭义且假设读者与作者同等主动。电影作为已完成产品，其运转约束观众积极参与。</li>
                <li><span class="theorist">罗兰·巴特</span>的《S/Z》（分析巴尔扎克《萨拉辛纳》）对文本概念深入影片分析有重要影响。
                    <ul>
                        <li>提出理论折中：用作品的<span class="key-concept">“复数性”</span>（"pluriel" d'une oeuvre）或<span class="key-concept">“多义性”</span>（polysémie）取代绝对的、无尽的复数性。</li>
                        <li>分析家任务：“拆散”、“摊平”文本以呈现其复数性、多义性，运用<span class="key-concept">内涵意指</span>（connotation）是有效工具。</li>
                        <li>系统性阅读保证内涵意指的客观性。每个内涵意指是“符码的起点”。</li>
                        <li>《S/Z》采取对古典文本既非主观也非客观的阅读方式，目标非描述文本结构，阅读永远未完成。</li>
                        <li>分析方法：将文本切分成<span class="key-concept">词组</span>（lexie），检视词组，指出其能指单元，归属到五个一般符码系统。分析保留文本表意系统的多义特质，形成“容量分析”式阅读。</li>
                        <li>巴特的符码概念较梅斯宽松，其“指涉性”符码和“象征性”符码是内涵意指的总集合。后将符码定义为“超文本的、制定结构概念组织标记的联合场域”，符码类别视所分析文本而定。</li>
                    </ul>
                </li>
            </ul>
            <div class="figure-container">
                 <svg width="600" height="250" viewBox="0 0 600 250" class="svg-diagram">
                    <defs>
                        <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                        </marker>
                    </defs>

                    <rect x="20" y="20" width="180" height="50" rx="5" ry="5" class="svg-box"/>
                    <text x="110" y="50" text-anchor="middle" class="svg-text">克丽斯特娃 "文本"</text>
                    <text x="110" y="30" text-anchor="middle" class="svg-text">(生产过程, 无尽阅读)</text>

                    <rect x="20" y="100" width="180" height="130" rx="5" ry="5" class="svg-box"/>
                    <text x="110" y="120" text-anchor="middle" class="svg-text"><tspan class="svg-highlight-text">巴特 《S/Z》</tspan></text>
                    <text x="110" y="145" text-anchor="middle" class="svg-text">- 复数性/多义性</text>
                    <text x="110" y="165" text-anchor="middle" class="svg-text">- 内涵意指</text>
                    <text x="110" y="185" text-anchor="middle" class="svg-text">- 词组 (Lexie)</text>
                    <text x="110" y="205" text-anchor="middle" class="svg-text">- 五大符码</text>

                    <rect x="300" y="100" width="280" height="80" rx="5" ry="5" class="svg-box"/>
                    <text x="440" y="120" text-anchor="middle" class="svg-text">影片文本分析实践</text>
                    <text x="440" y="145" text-anchor="middle" class="svg-text">(如：昆塞尔分析《M》)</text>
                    <text x="440" y="165" text-anchor="middle" class="svg-text">- 切分词组, 识别符码, 强调再阅读</text>


                    <line x1="200" y1="165" x2="300" y2="140" class="svg-arrow" marker-end="url(#arrowhead2)"/>
                    <text x="250" y="145" text-anchor="middle" class="svg-text">影响</text>
                    
                    <text x="300" y="45" text-anchor="middle" class="svg-text" style="font-size:12px; fill: #7f8c8d;">影片学对"文本"概念的接受相对谨慎</text>
                    <line x1="110" y1="70" x2="110" y2="100" class="svg-line" />
                    <line x1="200" y1="45" x2="300" y2="45" class="svg-line" />

                </svg>
                <p class="figure-caption">图2：文本概念的演变及其对影片文本分析的影响路径。</p>
            </div>

            <h3>2. 影片的文本分析</h3>
            <p>巴特《S/Z》融合文本概念两层含义，推动影片文本分析发展。基本概念包括指出文本表意成分、舒张内涵意指、鉴别潜在符码。</p>
            <p>以<span class="theorist">蒂埃里·昆塞尔</span>对弗里茨·朗《M》片头分析为例：</p>
            <ul>
                <li>采用巴特分析过程：依事件发展顺序切分<span class="key-concept">词组</span>（如《M》片头分为三个词组）。显示分镜无绝对价值，仅为特定分析的表现素材。</li>
                <li>界定每一词组功能（借由符码）：
                    <ul>
                        <li>词组一（字幕）：叙述符码、诠释符码（“M”字之谜）、象征符码。</li>
                        <li>词组二（孩子唱歌镜头）：视觉符码、叙述符码。</li>
                        <li>词组三（其余镜头）：电影符码（蒙太奇）表现焦心期待与落空。</li>
                    </ul>
                </li>
                <li>符码界定自由：部分撷取巴特《S/Z》五大符码（语义素符码、行动结果确立符码等，后者不适于电影），部分借自梅斯（构成符码、摄影机运动符码等），亦有普遍性（叙述符码）或特殊符码（视线符码）。</li>
                <li>文本模式内部无完备符码清单，分析者需在能指元素上引入“一个可能发展起来的符码”。</li>
                <li>强调<span class="key-concept">再阅读</span>（relecture）：虽依叙事顺序阅读，但援引影片后续部分与当前词组互相关照。</li>
            </ul>
            <p>昆塞尔的分析表明，文本分析非单纯“运用”模式。</p>
        </section>

        <section id="film-code-analysis">
            <h2>三、影片符码分析</h2>
            <h3>1. 符码概念的实质意义</h3>
            <p>符码概念假设影片在一个完整系统上存在许多独立自主的意义层次，已成当前影片分析基本共识。但在实质旨趣方面，因其普遍性高，无法成为立竿见影的工具。</p>
            <p>符码系统分析带来的问题：</p>
            <ul>
                <li>并非所有符码同等重要，普遍性方面呈现异质化。</li>
                <li>符码不以“纯粹”状态出现，影片既是符码体现成果，也是符码系统支配规则形成地。孤立符码困难。</li>
                <li>衡量影片艺术内在特质时，符码概念显得力不从心，更适用于系列式或“一般”电影。</li>
            </ul>

            <h3>2. 影片分析与符码系统分析</h3>
            <p>早期“符码”分析多集中于影片分段问题。</p>
            <ul>
                <li><span class="theorist">梅斯</span>分析罗齐耶《再见菲律宾》：将影片完整分段，每段落归入<span class="key-concept">“大组合段”</span>七种符码类型之一，标出界限。旨在“检视”大组合段类型，指出其非“绝对”符码，而是一个历史阶段（古典时期）。组合段出现频率可描绘影片风格特性（如《再见菲律宾》体现“新电影”及“戈达尔/直接电影”趋势）。</li>
                <li><span class="theorist">雷蒙·贝卢尔</span>分析明尼里《金粉世界》（《分段与分析》）：对影片做完整分镜表，指出以影片能指层面直接时间为段落区分标准，只能局部符合叙事情节。建议同时考虑：
                    <ul>
                        <li>与剧情叙事对应的<span class="key-concept">“超段落”</span>单元。</li>
                        <li>同一段落中因轻微情节改变再区分的<span class="key-concept">“次段落”</span>单元。</li>
                    </ul>
                    此建议指出大组合段是分析分段活动的一个面向，连接叙事符码（超段落）与多种随分段精密而起作用的符码（次段落）。
                </li>
            </ul>
            <p>符码的实效性是电影符码分析的核心问题。</p>
            <ul>
                <li><span class="theorist">米歇尔·马利</span>分析雷奈《穆里爱》“声音符码”：
                    <ul>
                        <li>声音分析轴线不独立，配乐需对应整部影片才有意义。</li>
                        <li>“声音符码”具复数形式，涉及音响构成、音画关系、对白等。整体性、多重性掌握优于音响分段。</li>
                        <li>从后制作或画外音角度分析收音方式可丰富结果。</li>
                    </ul>
                </li>
                <li><span class="theorist">雷蒙·贝卢尔</span>《明显性与符码》分析霍克斯《夜长梦多》短段落：通过对白和视线运作分析，指出段落如何呈现传统好莱坞剪接逻辑。更关注编码化的好莱坞电影如何在“明显性”外衣下掩藏其编码系统。</li>
            </ul>
        </section>

        <section id="exhaustive-and-endless-analysis">
            <h2>四、完尽的分析与无尽的分析 (影片符码分析的延伸探讨)</h2>
            
            <div class="emphasis-box">
                <h4>核心观点：完备详尽的分析是一种乌托邦幻影。</h4>
                <p>分析如同地平线，我们前进，它便后退。这意味着分析永远无法完尽，影片的分析探究源源不竭。</p>
            </div>

            <h3>1. 完尽分析的幻影</h3>
            <p>对文本做出详尽齐全的分析被视为乌托邦幻影。分析永远无法完尽。</p>
            <ul>
                <li>例：<span class="theorist">史蒂芬·希思</span>对威尔斯《历劫佳人》的详尽分析，后有学者<span class="theorist">约翰·洛克</span>发现片头快速闪过的导演影子，这一细节调整了叙事问题和陈述活动分析的分量，显示已有结论的分析仍可“重新出发”。</li>
            </ul>

            <h3>2. 影片的片段分析</h3>
            <p>对完备分析不可能实现的认知，影响了影片片段分析的兴起。</p>
            <ul>
                <li><span class="theorist">艾森斯坦</span>分析《战舰波将金》14个镜头：指出选择片段在于要求细节分析的精确度。
                    <ul>
                        <li>早期文本分析（60年代末）面临观阅困难，精确度是主要顾虑。剪接台或分析性放映机的出现解决了此问题。</li>
                        <li>片段分析成为整部影片分析的代替品——一种<span class="key-concept">抽样</span>，从部分得到整体效果（艾森斯坦“器质性”概念）。</li>
                        <li>片段分析也是一种<span class="key-concept">换喻</span>，可发展成更宏大研究架构（整部影片、作者风格、分析现象反思）。</li>
                    </ul>
                </li>
                <li><span class="theorist">雷蒙·贝卢尔</span>分析希区柯克《鸟》片段：所选片段具均质性与结构规律，助其剖析希区柯克电影乃至古典电影的对称、反复等同源效应。</li>
                <li><span class="theorist">雅克·奥蒙</span>分析艾森斯坦《总路线》、《恐怖的伊凡》场景，<span class="theorist">玛丽-克莱尔·罗帕尔</span>分析《穆里爱》片段，均旨在阐述整体。</li>
            </ul>
            <p>文本分析模式对片段分析地位正统化有重要作用。</p>
            <p><span class="key-concept">选择片段的标准</span>：</p>
            <ol>
                <li>是一个完整的片段（常与段落或超段落吻合）。</li>
                <li>结构上紧密扎实，具明显内在组织系统。</li>
                <li>足以代表整部影片（“代表性”依分析轴线和欲突出方面而定）。</li>
            </ol>
            <p>影片古典程度影响片段选择。风格统一的古典电影适合抽样。风格多样如《大国民》，则需选能衬托多样化风格的片段，避开“经典场面”。形式密实性常比剧情密实性更重要。</p>
            <ul>
                <li>例：<span class="theorist">尼克·布朗</span>分析福特《驿马车》选择客栈中微不足道的戏。</li>
                <li>片段比较分析：<span class="theorist">米歇尔·马利</span>比较卡尔内《北方旅馆》和泰希内《忆法兰西》片段，显现古典电影分镜特质及其现代演变。</li>
                <li>类型/时代研究：<span class="theorist">兰尼</span>、<span class="theorist">罗帕尔</span>、<span class="theorist">索蓝</span>研究30年代法国电影，注重“结构外观”（形式印记）。形式标准不等于形式主义。</li>
            </ul>

            <h3>3. 影片开场的分析与影片分析的开始</h3>
            <p>影片片头分析常见，原因：</p>
            <ul>
                <li>物质因素：早期拷贝分析时，片头易获取。</li>
                <li>叙事学因素（更重要）：叙事开场蕴藏丰富语义特质。</li>
            </ul>
            <p>排除通过比对首尾场戏系统分析影片的方法（仅能检视传统叙事电影平衡特性，即“同态调节”）。</p>
            <p>更有生产性的分析方式：将片头视为影片<span class="key-concept">“矩阵”</span>（matrice）模具。</p>
            <ul>
                <li><span class="theorist">玛丽-克莱尔·罗帕尔</span>分析艾森斯坦《十月》开头，视其为“展现片中史实化革命过程理论原型的矩阵”，具起动力量，强化发展转变可能。</li>
                <li><span class="theorist">蒂埃里·昆塞尔</span>分析《危险游戏》系列电影，指其字幕部分已是影片叙事及表现的矩阵。片头是影片文本最“现代”（复义）的部分，意义浮动，阅读迟疑。</li>
            </ul>
            <p>与矩阵概念相关的想法：影片从一开始形成的孕生力量。开头影像决定虚构状态及观众入戏状态，将观众从现实界置换到想像界（影片故事空间）。</p>
            <ul>
                <li><span class="theorist">罗歇·奥丁</span>分析雷诺阿《乡间的一日》“论观众如何进入虚构世界”：视虚构状态为“知与信的巧妙定量混合体”，分析此二条件如何在片头交互作用。</li>
                <li><span class="theorist">马克·维内</span>对照六部“黑色电影”片头，发现“快乐时光”后出现“揭露假象、摧毁信仰”片段，影片后续关系主角情境重塑的结构。</li>
            </ul>

            <h3>4. 分析客体的范围与分析的长度</h3>
            <p>分析长度应大于所分析文本长度，但非简单比例关系。分析长度取决于分析客体（常较抽象，如文本系统、符码研究），而非仅物质素材（影片）。</p>
            <ul>
                <li>短片段可有长篇论述：罗帕尔分析《十月》2分钟片段用40页；杜歇分析《愤怒》17镜头用32页；昆塞尔分析《危险游戏》片头62镜头用52页。</li>
                <li>整部影片分析通常需一本书长度。
                    <ul>
                        <li>汇编式：《十月》、《穆里爱》分析论著。</li>
                        <li>多轴线对应式：布维耶与勒塔分析《吸血鬼》（文化观点）。</li>
                        <li>时序进展式：古泽蒂分析《我所知道她的二三事》；达扬分析《驿马车》（受《S/Z》启发）。</li>
                        <li>混合策略：拉雷尔分析《家族的肖像》（分段+场面调度）。</li>
                        <li>特定方法论：汤普森分析《恐怖的伊凡》（新形式主义）。</li>
                    </ul>
                </li>
            </ul>
            <p>分析策略多样化原因：分析本质、影片性质、诉求对象。</p>
            <ul>
                <li>例：<span class="theorist">史蒂芬·希思</span>对《历劫佳人》分析，因发表刊物不同而有详略两版，结论一致但读者参与度不同。</li>
            </ul>
        </section>

        <section id="controversies-of-textual-analysis">
            <h2>五、文本分析丰富的争议性</h2>
            <p><span class="theorist">罗歇·奥丁</span>（1977）总结文本分析发展三点：</p>
            <ol>
                <li>不具评价性也不具规范性。</li>
                <li>侧重影片表意作用的分析。</li>
                <li>对分析方法本身与研究客体对象赋予同等重要地位（理论化严谨要求）。</li>
            </ol>
            <p>此总结未明确提与文本、符码概念、文本系统的关系，因其界定的是1967-1977年间分析总成果，而非全面影片文本分析。文本模式从未直接被援引运用，甚至可认为影片文本分析从不存在，而是其“神话”传承影响深远（对许多研究者代表负面形象）。</p>
            
            <div class="emphasis-box">
                <h4>对文本分析的主要批评论点：</h4>
                <ol>
                    <li><strong>适用范围局限：</strong>仅在分析叙事电影（尤其传统叙事电影）时有效，不适用实验电影等。
                        <ul><li><span class="theorist">多米尼克·诺盖</span>认为实验电影分析借鉴造型学“形式分析”或音乐学描述性研究更合适。此观点将“文本”隐约归为“文学文本”，曲解其可由视觉符号组成。</li></ul>
                    </li>
                    <li><strong>为剖析而剖析：</strong>助长研究者“抽丝剥茧的力比多”（libido decorticandi）。
                        <ul><li>不当分镜替代分析，误认其为“科学化”保证。</li><li><span class="theorist">贝卢尔</span>指出，分析应将作品中整体形式结构搅乱，以显现重组后的平衡秩序，面对的是审美与欲望的场域。</li></ul>
                    </li>
                    <li><strong>忽视语境：</strong>过分忽视影片制片过程与观众反应等背景。
                        <ul><li>有价值的文本分析，“遗忘影片环境”常刺激开放效果。仅当“内在”结构分析成为唯一始末点时才应受指摘。</li></ul>
                    </li>
                    <li><strong>简约化与僵化（最主要批评）：</strong>具将影片简约成其系统框架，从而“谋杀”影片的危险。
                        <ul><li><span class="theorist">贝卢尔</span>强调，真分析永远顾及观众与影片关系，不把影片简约成东西。</li></ul>
                    </li>
                </ol>
            </div>

            <p class="conclusion">
                上述批评多不具关键性。文本分析被泛用，与其说因过度形式化，不如说其原则给人过分简单错觉。它无使自己“积极化”特性，但具探索追寻美德。
                更重要地，电影符号学与文本分析体现了文本由电影内外表意系统网络构成的基本概念——影片分析不仅与“纯粹”影片或电影现象有关，也与<span class="key-concept">象征</span>（symbolique）有关。
                文本分析方法开辟了精神分析、解构分析、“整体延伸”分析等研究大道。“文本分析”一词或不再摩登，但仍是本书引介主要思想概念的方法论支柱。
            </p>
        </section>

        <section id="references" class="references">
            <h2>参考资料</h2>
            <h3>一、文本分析与结构主义</h3>
            <ul>
                <li>Claude Lévi-Strauss, <span class="work-title">La Pensée sauvage</span>, Paris, Plon, 1962.</li>
                <li>Roland Barthes, <span class="work-title">Mythologies</span>, Paris, Le Seuil, 1957. - <span class="work-title">S/Z</span>, Paris, Le Seuil, 1970.</li>
                <li>Christian Metz, <span class="work-title">Langage et Cinéma</span>, Paris, Larousse, 1971 , réed. Albatros, 1977.</li>
                </ul>
            <h3>二、影片文本</h3>
            <ul>
                <li>Julia Kristeva, <span class="work-title">Séméiotiké, Recherches pour une sémanalyse</span>, Paris, Le Seuil, 1969.</li>
                <li>Thierry Kuntzel, « Le travail du film » (sur le prologue de M le Maudit), in <span class="work-title">Communications</span> nº19, 1972.</li>
                </ul>
             <h3>三、影片符码分析</h3>
            <ul>
                <li>Christian Metz, « La grande syntagmatique de la bande-images » in <span class="work-title">Essais 1</span>, op. cit.</li>
                <li>Raymond Bellour, « Segmenter/analyser » in <span class="work-title">L' Analyse du film</span>, op, cit.</li>
                 </ul>
             <h3>四、完尽的分析与无尽的分析</h3>
            <ul>
                <li>Stephen Heath, in <span class="work-title">Screen</span>, 16, 1 et 2, 1975, op. cit. et « Système récit », in <span class="work-title">ça/Cinéma</span> nº 7/8, 1975.</li>
                <li>Raymond Bellour, « Les Oiseaux; analyse d' une séquence » <span class="work-title">Cahiers du Cinéma</span>, nº216, repris dans <span class="work-title">L' Analyse du film</span>, op. cit.</li>
                </ul>
            </section>
    </div>
</body>
</html>