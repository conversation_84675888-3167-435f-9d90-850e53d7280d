<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当代电影分析：文本分析</title>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --highlight-bg: #fffde7;
            --note-bg: #f0f7fb;
            --example-bg: #f0fff0;
            --border-color: #ddd;
            --light-bg: #f9f9f9;
        }
        
        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: var(--light-bg);
        }
        
        h1, h2, h3, h4, h5 {
            color: var(--secondary-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        h1 {
            text-align: center;
            font-size: 2.2em;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 1em;
        }
        
        h2 {
            font-size: 1.8em;
            border-bottom: 1px solid var(--primary-color);
            padding-bottom: 5px;
        }
        
        h3 {
            font-size: 1.4em;
            color: #2980b9;
        }
        
        h4 {
            font-size: 1.2em;
            color: #16a085;
        }
        
        h5 {
            font-size: 1.1em;
            color: #8e44ad;
        }
        
        p {
            margin-bottom: 1em;
        }
        
        .toc {
            background-color: #f8f9fa;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        
        .toc li {
            margin: 5px 0;
        }
        
        .concept {
            color: var(--accent-color);
            font-weight: bold;
        }
        
        .theorist {
            color: #8e44ad;
            font-weight: bold;
        }
        
        .quote {
            background-color: var(--note-bg);
            border-left: 5px solid var(--primary-color);
            padding: 15px;
            margin: 20px 0;
            font-style: italic;
        }
        
        .example {
            background-color: var(--example-bg);
            border-left: 5px solid #27ae60;
            padding: 15px;
            margin: 20px 0;
        }
        
        .note {
            background-color: #fff8e1;
            border-left: 5px solid #f39c12;
            padding: 15px;
            margin: 20px 0;
        }
        
        .highlight {
            background-color: var(--highlight-bg);
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .key-point {
            background-color: #e8f4fc;
            border: 1px solid #c1e0f7;
            border-radius: 5px;
            padding: 10px 15px;
            margin: 15px 0;
        }
        
        .diagram {
            text-align: center;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        footer {
            margin-top: 50px;
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            border-top: 1px solid var(--border-color);
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <h1>当代电影分析：文本分析</h1>
    
    <div class="toc">
        <h3>目录</h3>
        <ul>
            <li><a href="#intro">引言</a></li>
            <li><a href="#section1">一、文本分析与结构主义</a>
                <ul>
                    <li><a href="#section1-1">1. 基本概念</a></li>
                    <li><a href="#section1-2">2. 结构主义分析</a></li>
                </ul>
            </li>
            <li><a href="#section2">二、影片文本</a>
                <ul>
                    <li><a href="#section2-1">1. 文本概念的演变</a></li>
                    <li><a href="#section2-2">2. 影片的文本分析</a></li>
                </ul>
            </li>
            <li><a href="#section3">三、影片符码分析</a>
                <ul>
                    <li><a href="#section3-1">1. 符码概念的实质意义</a></li>
                    <li><a href="#section3-2">2. 影片分析与符码系统分析</a></li>
                </ul>
            </li>
            <li><a href="#section4">四、完尽分析与无尽的分析</a>
                <ul>
                    <li><a href="#section4-1">1. 完尽分析的幻影</a></li>
                    <li><a href="#section4-2">2. 影片的片段分析</a></li>
                    <li><a href="#section4-3">3. 影片开场的分析与影片分析的开始</a></li>
                    <li><a href="#section4-4">4. 分析客体的范围与分析的长度</a></li>
                </ul>
            </li>
            <li><a href="#section5">五、文本分析丰富的争议性</a></li>
            <li><a href="#conclusion">结论</a></li>
        </ul>
    </div>
    
    <section id="intro">
        <h2>引言</h2>
        <p>文本分析作为一种影片分析方法，其产生主要是为了解决影片分析面临的两个问题：在客体选择上的散漫性和在方法选择上的不确定性。文本分析方法在影片分析中占有重要地位，这主要基于两个原因：</p>
        <ul>
            <li>"文本"(texte)概念对影片及影片分析的统一性提出了最根本的问题</li>
            <li>文本分析曾几乎变成影片分析的一般代名词</li>
        </ul>
        
        <div class="key-point">
            <p>文本分析在本质上并非与其他分析方法截然不同，它也不能提供一条解决各种困难的康庄大道，但它确实为影片分析提供了一种系统化的思考框架和方法论支持。</p>
        </div>
    </section>
    
    <section id="section1">
        <h2 id="section1">一、文本分析与结构主义</h2>
        
        <section id="section1-1">
            <h3>1. 基本概念</h3>
            
            <p>结构主义的中心思想是<span class="concept">结构</span>的概念。结构主义分析家试图辨明隐藏在意义生产过程中的<span class="concept">"深层"结构</span>(structure "profonde")，这些深层结构能够解释表面形式的变化和特征。</p>
            
            <div class="diagram">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
                            <path d="M0,0 L0,6 L9,3 z" fill="#333"/>
                        </marker>
                    </defs>
                    
                    <!-- 上层框 - 表层结构 -->
                    <rect x="150" y="50" width="300" height="80" rx="5" ry="5" fill="#f8d7da" stroke="#721c24" stroke-width="2"/>
                    <text x="300" y="90" text-anchor="middle" font-size="16">表层形式</text>
                    <text x="300" y="115" text-anchor="middle" font-size="12">(各种看似不同的文本表现)</text>
                    
                    <!-- 下层框 - 深层结构 -->
                    <rect x="150" y="200" width="300" height="80" rx="5" ry="5" fill="#d1ecf1" stroke="#0c5460" stroke-width="2"/>
                    <text x="300" y="240" text-anchor="middle" font-size="16">深层结构</text>
                    <text x="300" y="265" text-anchor="middle" font-size="12">(结构主义者试图揭示的本质)</text>
                    
                    <!-- 连接箭头 -->
                    <line x1="300" y1="130" x2="300" y2="200" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
                    <text x="330" y="170" font-size="12">分析关系</text>
                </svg>
            </div>
            
            <p><span class="theorist">列维·斯特劳斯</span>(Claude Lévi-Strauss)是结构主义的重要代表人物。他通过研究神话发现，表面上看似任意复杂的叙述实际上具有强烈的规律性和系统性。列维·斯特劳斯推论，表面上极其不同的意义生产活动，实际上可能共享相同的深层结构。</p>
            
            <div class="quote">
                <p>"不论在思辨层面或是实践层面，差别的明显性较其内容更重要得多：只要它存在，就会形成一种有用的区别系统，如同一个可以运用在一个初窥乍看之下完续而难以理解的文本之上的架构，加以切割、造成对比，亦即引进讯息的形式条件，以解读这个文本。"(列维·斯特劳斯，《野性的思维》)</p>
            </div>
            
            <p>结构主义深受语言学影响，特别是<span class="theorist">索绪尔</span>(Ferdinand de Saussure)的语言学理论。结构经常被视为<span class="concept">二元对立系统</span>(oppositions binaires)，索绪尔认为语言本身就是建立在这种对立模式上的。</p>
            
            <div class="note">
                <h4>结构主义的关键概念：</h4>
                <ul>
                    <li>深层结构：隐藏在表面现象背后的规律系统</li>
                    <li>二元对立：通过差异和对立建立意义</li>
                    <li>语言模型：将语言学模型应用于各种文化现象</li>
                    <li>意义生产：关注意义如何在结构中产生</li>
                </ul>
            </div>
            
            <p>其他重要的结构主义理论家还包括：</p>
            <ul>
                <li><span class="theorist">拉康</span>(Jacques Lacan)宣称"无意识有如语言活动一般的结构"</li>
                <li><span class="theorist">罗曼·雅各布森</span>(Roman Jakobson)最早以结构主义分析诗歌作品</li>
            </ul>
        </section>
        
        <section id="section1-2">
            <h3>2. 结构主义分析</h3>
            
            <p>电影的文本分析衍生自结构主义分析，虽然两者之间的关系有时并不十分稳定。结构主义分析可以应用于各种意义生产活动，从神话到文学艺术创作，包括电影。</p>
            
            <div class="example">
                <h4>结构主义影片分析实例：《2001太空漫游》</h4>
                <p>让-保罗·杜蒙(Jean-Paul Dumont)与让·莫诺(Jean Monod)对库布里克的《2001太空漫游》进行了"列维·斯特劳斯式"的影片分析。他们尝试"尽可能地少用词汇及文法成分"来厘清此片的"语意结构"(structure sémantique)，并将其视为"新版"星宿神话。</p>
                <p>这一分析的特点：</p>
                <ul>
                    <li>重点关注影片的声带部分而非视觉部分</li>
                    <li>按照叙事时序将具代表性的元素组织起来</li>
                    <li>在对立/区别系统下分析这些元素</li>
                    <li>方法论上否认最终意义的存在</li>
                    <li>关注"语言活动内部能指与能指之间的关系"</li>
                </ul>
            </div>
            
            <p>对文本分析思潮影响最深远的三位理论家是<span class="theorist">艾柯</span>(Umberto Eco)、<span class="theorist">罗兰·巴特</span>和<span class="theorist">梅斯</span>。</p>
            
            <h4>艾柯的贡献</h4>
            <p>艾柯在《不在的结构》(1968)中提出了意义与沟通现象组成<span class="concept">符号系统</span>(systèmes de signe)的观念。他认为，通过将个别讯息与规范讯息传输、理解的一般<span class="concept">符码</span>(codes)联系起来，可以对符号系统进行研究。</p>
            
            <h4>罗兰·巴特的贡献</h4>
            <p>巴特对电影文本分析的影响更为直接。他的《影像的修辞》分析了广告影像，侧重于意义层面而非沟通层面。巴特探索了影像中的<span class="concept">内涵意旨</span>(connotation)，关注影像内在意义网络中内涵意旨的地位。</p>
            <p>巴特在《神话学》(1957)中已分析了各种不同"文本"中的意识形态表现，指出社会上广泛流传的产品（包括电影）都承载着系统化的意涵。他在1960年的文章中提出了电影结构主义分析的基本原则。</p>
            
            <h4>梅斯的贡献</h4>
            <p>梅斯在《语言活动与电影》(1971)中针对巴特提出的问题尝试提供系统化答案，深刻影响了电影分析的理论与实务发展。</p>
            
            <div class="key-point">
                <h4>梅斯对符码概念的定义：</h4>
                <p>符码涵括所有影片表意作用的规律及系统化现象，某种程度上替代了电影的"语言"(langue)。符码的特点：</p>
                <ul>
                    <li>符码不是绝对对等于语言，而是借着组合关系形成的</li>
                    <li>理论上每个符码都可独立成"纯粹状态"，但实际上永远以共生方式结合运作</li>
                    <li>符码可描述电影语言活动内部的多重表意功能</li>
                    <li>有些符码比其他符码更"基本"、更重要，但都不能像语言一样扮演组织角色</li>
                    <li>符码既可检视某部特定影片与大部分电影共通的普遍现象，也可检视局部现象</li>
                </ul>
            </div>
        </section>
    </section>
    
    <section id="section2">
        <h2 id="section2">二、影片文本</h2>
        
        <section id="section2-1">
            <h3>1. 文本概念的演变</h3>
            
            <p>影片分析从电影结构主义符号学(sémiologie structurale)撷取了三个基本概念：</p>
            
            <div class="diagram">
                <svg width="600" height="350" viewBox="0 0 600 350">
                    <rect x="50" y="50" width="500" height="80" rx="10" ry="10" fill="#d4edda" stroke="#28a745" stroke-width="2"/>
                    <text x="300" y="90" text-anchor="middle" font-size="18" font-weight="bold">影片文本 (le texte filmique)</text>
                    <text x="300" y="115" text-anchor="middle" font-size="14">作为"言谈齐一性之实际体现"的具体影片</text>
                    
                    <rect x="50" y="150" width="500" height="80" rx="10" ry="10" fill="#cce5ff" stroke="#0d6efd" stroke-width="2"/>
                    <text x="300" y="190" text-anchor="middle" font-size="18" font-weight="bold">影片文本系统 (le système textuel filmique)</text>
                    <text x="300" y="215" text-anchor="middle" font-size="14">每部影片所独有的结构模式</text>
                    
                    <rect x="50" y="250" width="500" height="80" rx="10" ry="10" fill="#fff3cd" stroke="#ffc107" stroke-width="2"/>
                    <text x="300" y="290" text-anchor="middle" font-size="18" font-weight="bold">符码 (le code)</text>
                    <text x="300" y="315" text-anchor="middle" font-size="14">可应用于不同文本的普遍性系统</text>
                </svg>
            </div>
            
            <p>除了结构主义式的意涵外，"文本"在20世纪60年代末期还获得了新的含义，特别是在现代文学理论中。</p>
            
            <p><span class="theorist">茱莉亚·克丽斯特娃</span>(Julia Kristeva)在《Tel Quel》上提出，文本不是陈列在书店里的作品，而是笔体(écriture)本身的"空间"(espace)。在这个定义下，文本被视为意义生产的（无尽的）过程，潜在着无尽且无数的阅读空间活动。</p>
            
            <p>这种"文本"观念最初并未被影片学广泛接受，原因有二：</p>
            <ol>
                <li>这是一个狭义概念，难以适应所有文学或电影作品</li>
                <li>它假设读者与作者同样具有"生产力"，而电影媒介本身限制了观众的主动参与</li>
            </ol>
            
            <p><span class="theorist">罗兰·巴特</span>的《S/Z》(1970)是文本概念发展的转折点。巴特提出了折中做法，他把文本<span class="concept">"抄体"</span>(scriptible)视为作品完结的否定，而提出作品的<span class="concept">"复数性"</span>("pluriel" d'une oeuvre)的概念。</p>
            
            <div class="note">
                <h4>巴特的文本分析方法</h4>
                <p>在《S/Z》中，巴特采用了一种既非"主观"也非"客观"的阅读态度，通过"慢速"分析的方式呈现古典文本结构的可逆性。基本步骤是：</p>
                <ol>
                    <li>将文本切分为<span class="concept">词组</span>(lexie)，即长短不拘的文本片段</li>
                    <li>按顺序检视这些词组，指出内涵意指的能指单元</li>
                    <li>将每个内涵意指归属到五个一般符码系统中</li>
                    <li>拒绝做出总结，保留文本表意系统的多义特质</li>
                </ol>
                <p>这种分析拒绝将文本简化为单一意义，强调文本的开放性和多义性。</p>
            </div>
            
            <p>巴特的符码概念相当松散，与梅斯相比更为宽泛。到了1973年，巴特将符码定义为"超文本的(supra-textuel)、制定结构概念组织标记的联合场域"，使符码的类别更加开放和灵活。</p>
        </section>
        
        <section id="section2-2">
            <h3>2. 影片的文本分析</h3>
            
            <p>巴特的《S/Z》成功融合了文本概念的多重含义，对影片文本分析的发展起到了推波助澜的作用。巴特模式的主要魅力在于它背离了传统上建构固定、详尽评述的研究系统，代之以"开放"的态度，摒弃了分析必须得出最终意义的传统观点。</p>
            
            <div class="example">
                <h4>文本分析实例：《M》</h4>
                <p>蒂埃里·昆塞尔对弗里茨·朗的《M》所作的片头分析(1972年)是早期影片文本分析的典范。他将影片片头区分为三个词组：</p>
                <ol>
                    <li>影片字幕部分（特别是影片标题）</li>
                    <li>第一个镜头（孩子唱儿歌，妇人提洗衣篮）</li>
                    <li>片段中其余的全部镜头</li>
                </ol>
                
                <p>昆塞尔为每个词组定义了不同的符码功能：</p>
                <ul>
                    <li>第一词组：叙述符码、诠释符码("M"字之谜)、象征符码</li>
                    <li>第二词组：视觉符码(摄影机运动、画面构图等)、叙述符码</li>
                    <li>第三词组：电影符码(特别是蒙太奇符码)，表现期待与落空</li>
                </ul>
                
                <p>这个分析展示了文本分析的几个特点：</p>
                <ul>
                    <li>符码的选择非常灵活，没有固定的清单</li>
                    <li>分析者需在每个能指元素上引入"可能发展的符码"</li>
                    <li>虽按叙事顺序分析，但不断与影片后续部分互相关照</li>
                    <li>实践了巴特强调的"再阅读"(relecture)观念</li>
                </ul>
            </div>
            
            <p>昆塞尔的分析明确显示，文本分析绝非简单套用固定模式，而是一种需要分析者积极参与、不断调整的动态过程。</p>
        </section>
    </section>
    
    <section id="section3">
        <h2 id="section3">三、影片符码分析</h2>
        
        <section id="section3-1">
            <h3>1. 符码概念的实质意义</h3>
            <p>符码这一概念直至今日仍然具有相当重要的理论旨趣 — 符码概念假设影片在一个完整的系统上，存在着许多独立自主的意义层次 — 基本上已经变成当前影片分析必备的基本共识，可是在实质旨趣方面就没有那么明显的力量了，由于符码概念的普遍性很高，所以无法成为一个在效率上能立竿见影的工具。</p>
            
            <p>符码系统分析带来的问题至少有以下三种：</p>
            
            <p>首先就是前面已经提过的一点，不是所有的符码都具有同等的重要性（具体运用时更可以看出它们的不对等关系），亦即各个符码概念在普遍性方面呈现出一种异质化的现象；</p>
            
            <p>其次，符码从来不以"纯粹"状态出现，因为一部影片如果说是符码具体体现的成果，那么它也同时是符码系统支配规则形成的所在地点：一部影片之能创造符码，就如同它能运用符码一样。因此，想要具体地把符码"孤立"出来，经常是一件很困难的事。</p>
            
            <p>最后一个问题是，当我们想要衡量一部影片的艺术内在特质时，运用符码概念多多少少就会显得有点力不从心。伟大的影片总是充满原创性，是对古典手法的彻底反动与决裂，因此符码分析基本上比较适用于系列式的影片，或所谓的"一般"电影。</p>
            
            <p>上述这些符码概念旨趣及问题，都会在下面章节的阐述过程中一一呈现。</p>
            
            <h3>2. 影片分析与符码系统分析</h3>
            <p>前面（第二章第二部分第2节）详谈过的影片分段方法 — 一种具体而且可用的做法 — 本质上是以理论的姿态落实到影片分析的范畴中，而且借此证明电影中的确存在着符码。我们在本节中列举的早期"符码"分析范例，也顺应这个走向，先集中在影片分段问题的分析上面。</p>
            
            <p>第一个将"大组合段"模式应用在分析研究上的就是梅斯本人，他选择了法国导演雅克·罗齐耶（Jacques Rozier）的片子《再见菲律宾》（Adieu Philippine）作为分析对象。梅斯将影片做了完整的分段，并把每一个段落（segment）并到大组合段界定出的七种符码类型中的一种，标出段落与段落之间的标点符号与界限。在整个分段的进行过程中，梅斯特别借着机会指出段落始终的界定（从影片的最开头处）和段落性质的确定（景和场之间的分别，或是交替轮换等问题）方面的困难。</p>
            
            <div class="image-caption">
                <figure>
                    <img src="img/adieu_philippine.jpg" alt="雅克·罗齐耶的《再见菲律宾》(1962)剧照">
                    <figcaption>雅克·罗齐耶的《再见菲律宾》(1962)剧照</figcaption>
                </figure>
            </div>
            
            <p>除了从影片本身导出的结论之外，这篇分析的最终鹄的可以说就是在"检视"大组合段的类型，加以琢磨、讨论，进而修正。而且借着《再见菲律宾》的分段分析，明确地表示大组合段（不像影像类比关系这样的符码一样）并不是一个"绝对的"符码，它只是这个语言系统在理论及实务发展过程中的一个历史阶段而已（大致上符合"古典时期"）。至于在一部特定影片中，某一类组合段出现的次数频繁与否，完全取决于这部影片在电影形式发展史中的地位。相对的，分析者正可以借由组合段的出现频率描绘该片的风格特性。</p>
            
            <p>因此，梅斯特别在段落类型的频率统计以及影片的风格归属问题之间的协调对应上，对分段分析做出了以下的评论：</p>
            
            <blockquote>
                "从《再见菲律宾》一片各种段落类型出现频率的多寡与否的概述过程中，我们可以重新确认在直观批评上所感受到的影片风格 — 一部典型的'新电影'（形式的解放、对过度'修辞'手法的反动、影片叙事呈现出'简单化'、'透明化'趋向等），以及存在于这类新电影之内，我们可以称之为"戈达尔/直接电影"（Godard-cinema direct）的趋势（强调语言成分、场景的重要性、整体'写实精神'和真正的新形态蒙太奇观念的诞生）。"
            </blockquote>
            
            <p>我们在第二章第二部分第4节中讨论过雷蒙·贝卢尔对于文森特·明尼里的《金粉世界》一片所作的分析，也是针对这个问题进行一种全面性的再检讨（其文标题"分段与分析"[Segmenter/Analyser]已经明确地标举出它的论述企图），在理论和分析两个层次目标上，将这部片子从头到尾做了一份完整的分镜表。贝卢尔在反思分段过程中遇到困难时（尽管该片的"古典程度"很高），指出"以影片能指层面中多重的直接时间作为段落区分的标准，只能局部地符合叙事情节的开展过程及戏剧行动的时间序列"。</p>
            
            <p>所以，贝卢尔建议同时考虑使用与剧情叙事相对应的"超段落"单元（unités sur-segmentales，梅斯在《再见菲律宾》的分析中已试探这种可能性），以及同一段落中由于"轻微的"情节改变而再加区分的"次段落"单元（unités sous-segmentales，例如某一剧中人物中途出现或离开），来处理属于古典传统的叙事电影。贝卢尔的建议具有相当重要的理论价值（几乎所有叙事电影都可以运用这种分段方式），特别是它明白地指出，大组合段实际上是分析分段活动的一种面向（一种符码），一头连接叙事符码（超段落），另一头（次段落）则连接多种随着分段愈来愈精密而起作用的符码，进而剖析仅包含少数能指构成的、小的文本片段（本书第二章第二部分第2节关于《艾丽莎吾爱》的分段，由剧本大的连戏单元向下细分成愈来愈短的故事空间单元，基本上都是相同的概念）。</p>
            
            <p>除了这两篇著名的奠基性学术论文之外，还有更多的篇章援用大组合段或是贝卢尔的模式来分析电影，不过，它们都没有任何创新或超越这两篇论文之处，而一部电影的（超/次）段落区分也一直都是评鉴其风格归属问题的有力指标。接着要谈的是另外一个跟影片分析如影随形、也是所有的电影符码分析必然关心的问题 — 符码的实效性如何。</p>
            
            <p>前面提到过的米歇尔·马利对阿兰·雷奈的片子《穆里爱》所作的分析，就明显地触及了这个问题。在这篇从许多不同的角度勘验整部影片的通盘分析中，特别开辟了一个章节专门探讨该片的"声音符码"问题，它的两个分析重点诉求基本上很具有普遍性：</p>
            
            <p>"突显声音分析轴线的重要性，并不代表这个轴线是独立自主的"，所以该篇分析的结论之一就是影片中的配乐不具独立自主功能，它只有在对应整部影片的时候才有意义；</p>
            
            <p>"声音符码"具有复数的形式，其原因在于它所涉及的范围涵括音响构成、音画之间的关系（视听两向度的相互配合）等各种声音类比关系方面的问题，以及影片中的对白问题，因此这个分析轴不会是单一方向的。影片声音方面的研究角度与分段问题的研究角度不同，如果从整体性及多重性上去掌握声音符码，会比做音响的分段所得到的成果好得多。从这个认知角度出发，电影配乐的介入方式剖析就能借由强烈的抽象结构捕捉到整体的影片意蕴，从后制作（postsynchronisation，探讨影片与写实再现的关系）或画外音的角度去分析影片收音的方式，也能更加丰富分析的结果。</p>
            
            <p>马利在有关声音分析的章节中，花了很长的篇幅定义四种"声音符码"，并进行归类，呈现理论化的走向。从"符码"的角度去研究电影的析论不在少数，它们经常超越了原先预设的蓝图，侧面进行与最初架构有关联的其他方面的研究，进而扩展到符码本质的问题研究上头。</p>
            
            <p>雷蒙·贝卢尔曾发表过一篇名为《明显性与符码》（L'Evidence et le Code）的文章，专门分析霍华德·霍克斯的片子《夜长梦多》（1946）中一个很短的段落（仅包含12个镜头）。他所选择的分析客体篇幅短小、结构简单（相当于大组合段分类中的一场戏）、视觉内容减至最低程度（一系列的正/反拍镜头[champ/contrechamp]）。从对白和视线运作的重点分析中，贝卢尔指出这个段落如何透过两个演员之间的话语及视线交换，呈现出"传统"好莱坞电影的剪接逻辑。除了这个特殊符码的研究之外，分析家更感兴趣的倒是编码化（code）的好莱坞电影如何在"明显性"的外衣下掩藏它的编码系统。</p>
            
            <p>正如前面（第一章第三部分）所述，影片分析多少都有贴近理论的企图，这点毋庸赘言，而有关符码的严谨研究也更加强化了这样的一个趋向。</p>
        </section>
        
        <section id="section3-2">
            <h3>2. 影片分析与符码系统分析</h3>
            <p>前面（第二章第二部分第2节）详谈过的影片分段方法 — 一种具体而且可用的做法 — 本质上是以理论的姿态落实到影片分析的范畴中，而且借此证明电影中的确存在着符码。我们在本节中列举的早期"符码"分析范例，也顺应这个走向，先集中在影片分段问题的分析上面。</p>
            
            <p>第一个将"大组合段"模式应用在分析研究上的就是梅斯本人，他选择了法国导演雅克·罗齐耶（Jacques Rozier）的片子《再见菲律宾》（Adieu Philippine）作为分析对象。梅斯将影片做了完整的分段，并把每一个段落（segment）并到大组合段界定出的七种符码类型中的一种，标出段落与段落之间的标点符号与界限。在整个分段的进行过程中，梅斯特别借着机会指出段落始终的界定（从影片的最开头处）和段落性质的确定（景和场之间的分别，或是交替轮换等问题）方面的困难。</p>
            
            <div class="image-caption">
                <figure>
                    <img src="img/adieu_philippine.jpg" alt="雅克·罗齐耶的《再见菲律宾》(1962)剧照">
                    <figcaption>雅克·罗齐耶的《再见菲律宾》(1962)剧照</figcaption>
                </figure>
            </div>
            
            <p>除了从影片本身导出的结论之外，这篇分析的最终鹄的可以说就是在"检视"大组合段的类型，加以琢磨、讨论，进而修正。而且借着《再见菲律宾》的分段分析，明确地表示大组合段（不像影像类比关系这样的符码一样）并不是一个"绝对的"符码，它只是这个语言系统在理论及实务发展过程中的一个历史阶段而已（大致上符合"古典时期"）。至于在一部特定影片中，某一类组合段出现的次数频繁与否，完全取决于这部影片在电影形式发展史中的地位。相对的，分析者正可以借由组合段的出现频率描绘该片的风格特性。</p>
            
            <p>因此，梅斯特别在段落类型的频率统计以及影片的风格归属问题之间的协调对应上，对分段分析做出了以下的评论：</p>
            
            <blockquote>
                "从《再见菲律宾》一片各种段落类型出现频率的多寡与否的概述过程中，我们可以重新确认在直观批评上所感受到的影片风格 — 一部典型的'新电影'（形式的解放、对过度'修辞'手法的反动、影片叙事呈现出'简单化'、'透明化'趋向等），以及存在于这类新电影之内，我们可以称之为"戈达尔/直接电影"（Godard-cinema direct）的趋势（强调语言成分、场景的重要性、整体'写实精神'和真正的新形态蒙太奇观念的诞生）。"
            </blockquote>
            
            <p>我们在第二章第二部分第4节中讨论过雷蒙·贝卢尔对于文森特·明尼里的《金粉世界》一片所作的分析，也是针对这个问题进行一种全面性的再检讨（其文标题"分段与分析"[Segmenter/Analyser]已经明确地标举出它的论述企图），在理论和分析两个层次目标上，将这部片子从头到尾做了一份完整的分镜表。贝卢尔在反思分段过程中遇到困难时（尽管该片的"古典程度"很高），指出"以影片能指层面中多重的直接时间作为段落区分的标准，只能局部地符合叙事情节的开展过程及戏剧行动的时间序列"。</p>
            
            <p>所以，贝卢尔建议同时考虑使用与剧情叙事相对应的"超段落"单元（unités sur-segmentales，梅斯在《再见菲律宾》的分析中已试探这种可能性），以及同一段落中由于"轻微的"情节改变而再加区分的"次段落"单元（unités sous-segmentales，例如某一剧中人物中途出现或离开），来处理属于古典传统的叙事电影。贝卢尔的建议具有相当重要的理论价值（几乎所有叙事电影都可以运用这种分段方式），特别是它明白地指出，大组合段实际上是分析分段活动的一种面向（一种符码），一头连接叙事符码（超段落），另一头（次段落）则连接多种随着分段愈来愈精密而起作用的符码，进而剖析仅包含少数能指构成的、小的文本片段（本书第二章第二部分第2节关于《艾丽莎吾爱》的分段，由剧本大的连戏单元向下细分成愈来愈短的故事空间单元，基本上都是相同的概念）。</p>
            
            <p>除了这两篇著名的奠基性学术论文之外，还有更多的篇章援用大组合段或是贝卢尔的模式来分析电影，不过，它们都没有任何创新或超越这两篇论文之处，而一部电影的（超/次）段落区分也一直都是评鉴其风格归属问题的有力指标。接着要谈的是另外一个跟影片分析如影随形、也是所有的电影符码分析必然关心的问题 — 符码的实效性如何。</p>
            
            <p>前面提到过的米歇尔·马利对阿兰·雷奈的片子《穆里爱》所作的分析，就明显地触及了这个问题。在这篇从许多不同的角度勘验整部影片的通盘分析中，特别开辟了一个章节专门探讨该片的"声音符码"问题，它的两个分析重点诉求基本上很具有普遍性：</p>
            
            <p>"突显声音分析轴线的重要性，并不代表这个轴线是独立自主的"，所以该篇分析的结论之一就是影片中的配乐不具独立自主功能，它只有在对应整部影片的时候才有意义；</p>
            
            <p>"声音符码"具有复数的形式，其原因在于它所涉及的范围涵括音响构成、音画之间的关系（视听两向度的相互配合）等各种声音类比关系方面的问题，以及影片中的对白问题，因此这个分析轴不会是单一方向的。影片声音方面的研究角度与分段问题的研究角度不同，如果从整体性及多重性上去掌握声音符码，会比做音响的分段所得到的成果好得多。从这个认知角度出发，电影配乐的介入方式剖析就能借由强烈的抽象结构捕捉到整体的影片意蕴，从后制作（postsynchronisation，探讨影片与写实再现的关系）或画外音的角度去分析影片收音的方式，也能更加丰富分析的结果。</p>
            
            <p>马利在有关声音分析的章节中，花了很长的篇幅定义四种"声音符码"，并进行归类，呈现理论化的走向。从"符码"的角度去研究电影的析论不在少数，它们经常超越了原先预设的蓝图，侧面进行与最初架构有关联的其他方面的研究，进而扩展到符码本质的问题研究上头。</p>
            
            <p>雷蒙·贝卢尔曾发表过一篇名为《明显性与符码》（L'Evidence et le Code）的文章，专门分析霍华德·霍克斯的片子《夜长梦多》（1946）中一个很短的段落（仅包含12个镜头）。他所选择的分析客体篇幅短小、结构简单（相当于大组合段分类中的一场戏）、视觉内容减至最低程度（一系列的正/反拍镜头[champ/contrechamp]）。从对白和视线运作的重点分析中，贝卢尔指出这个段落如何透过两个演员之间的话语及视线交换，呈现出"传统"好莱坞电影的剪接逻辑。除了这个特殊符码的研究之外，分析家更感兴趣的倒是编码化（code）的好莱坞电影如何在"明显性"的外衣下掩藏它的编码系统。</p>
            
            <p>正如前面（第一章第三部分）所述，影片分析多少都有贴近理论的企图，这点毋庸赘言，而有关符码的严谨研究也更加强化了这样的一个趋向。</p>
        </section>
    </section>
    
    <section id="section4">
        <h2 id="section4">四、完尽的分析与无尽的分析</h2>
        
        <section id="section4-1">
            <h3>1. 完尽分析的幻影</h3>
            <p>文本分析的中心问题，就是分析本身是否很完备，是否称得上是一篇意义焕然彰显（表意系统）的论文，这点我们已经在前面的章节申述过。不过在所有文本分析的研究面向中，这种对文本做出详尽而齐全的分析的想法，一直就被当作是一种乌托邦幻影看待 — 这是一件可以想像，但不可能出现在现实中的事。如果换一个比较不那么负面的看法，我们可以说，它就好像是分析的地平线一样，一旦我们向前进，它就会往后退得更远。</p>
            
            <p>这个所有分析者都会触及的问题，在分析的实践活动中具有重大的意义，它意味着分析是永远无法完尽的。不管是长篇大论，还是简短的文本分析（只谈一小段影片），影片的分析探究永远是源源不竭的。</p>
            
            <p>举个简单的例子来说，电影学者史蒂芬·希思对奥森·威尔斯的片子《历劫佳人》所作的分析篇幅之长，可谓罕见。近期另一位学者约翰·洛克（John Locke）提出新的观点，认为在这部片子开始的第一个镜头（一个相当有名的、繁复的摄影机运动，同时呈现片中年轻情侣和边界小镇的问题架构）中，有一个为时仅有数个画格、快速闪过的阴影，应是饰演昆兰探长（Quinlan）的威尔斯本人的影子。这个细节问题当然不会改变希思"全部"的分析结果，它只是调整了叙事问题（如果昆兰一开始就以画外的形式出现，就代表他所知的比我们所能想像的还多）和特别是陈述活动分析（analyse de l'enonciation，既然是导演本人的影子，尤其是嗜以出现/不在[presence/absence]的方式给片子以标记的大导演威尔斯）问题的分量而已。另一方面，这个例子也显示出，像希思这种已经提出结论的分析，还是随时有可能再"重新出发"的。</p>
            
            <p>同样的例子还有很多，不过，并不一定只有在新东西或观点被提出来谈论的时候，我们才能重新探讨、延伸或反驳一篇既定的分析。影片分析是永无止境的，这种特性在实际的分析活动中，尤其是在分析客体的选择或分析范畴的界定上，发挥了不小的作用。尽管对一部特定的影片做出面面俱到的完备分析在实际的分析活动中不可能实现，但这个企图仍盘踞在分析者的心中，连带影响了影片片段分析（analyses de fragments）的兴起，这是我们在下面要谈的主题。</p>
        </section>
        
        <section id="section4-2">
            <h3>2. 影片的片段分析</h3>
            <p>我们在第一章第二部分第1节引述了艾森斯坦的《战舰波将金》一片中的14个镜头分析。在这篇与文本分析本质上非常不一样的（它注重美学及政治层面）析论过程中，艾森斯坦已经指出了我们所讨论的几个问题：</p>
            
            <p>1. 选择分析一个影片片段的主要因素在于要求细节分析的精确程度。以艾森斯坦的例子而言，由于他分析的是自己的作品，对影片细节的正确认知或是取得观阅这部片子的途径，都不会产生太大的问题。反之，文本分析发展初期（20世纪60年代末）对于细节问题的顾虑就完全不同了。今天拜录影带拷贝普及之赐，我们想看一部片子也许没什么困难，因此就很难想像20多年前那些优秀的分析家为了分析一部片子，只能一再地回到电影院去重看这部电影，并在整个黑暗的放映过程中埋头做着笔记。</p>
            
            <p>雷蒙·贝卢尔那篇著名的希区柯克影片《鸟》的片段分析，就是以综合发行公司提供的分镜表以及他在影片放映过程中所做的笔记，构成了一个分镜分析底本，这种方式本身的困难（以及免不了会出的错）是可以猜想得到的。自从借着剪接台或分析性放映机观阅影片拷贝的可能性慢慢出现之后，长久以来分析者所关心的精确度难题，如一线曙光般，终于获得解决。有限的记忆力，或者在黑暗的放映厅内写下的潦草字体等问题，全部都被这些可以再检视的可能性取代，一切都"不会漏掉"了。影片的片段分析，相对地更容易掌握，成为理想的分析对象。</p>
            
            <p>2. 由于在精确度的要求上往前跨了一大步，影片的片段就分析观点而言，很快就变成一整部影片分析的最佳代替品 — 一种抽样（就好像化学一样），从抽取的部分能得到整体分析的效果。艾森斯坦在他所谓的"器质性"（organicité）概念中已显现出同样的想法。最初期的文本分析所关心的议题也非常类似，除了被分析的段落之外，影片的片段分析也同时是一种换喻，可以发展成更宏大的研究架构（例如整部影片的分析、电影作者的风格研究，或是整体分析现象的反思等）。</p>
            
            <p>雷蒙·贝卢尔所选的《鸟》影片中的那一个片段，不仅具有充分的均质性（homogeneité）及结构上的规律特性，而且也能够让他深入剖析构成所有希区柯克电影的对称、反复等同源效应（并进而扩展到所有的古典电影上头）。</p>
            
            <p>下面这两个例子表现得更清楚：雅克·奧蒙特別在艾森斯坦的《总路线》（La Ligne generale, 1929）和《恐怖的伊凡》（Ivan le Terrible）的几个场景分析中，将片段与两部整体影片对应起来，以显现艾森斯坦电影理论系统的坚实；同样的，玛丽-克莱尔·罗帕尔所作关于《穆里爱》的片段分析，也旨在阐述整部片子的"笔体"（ecriture）。</p>
            
            <p>文本分析模式在这点上发挥了极为重要的作用，没有它作为支柱，影片片段分析的方式恐怕还得被一再提出来反省论证。同样的，文本分析模式在学术界能引发这么多的回响，得到这么可观的成果，主要的原因之一，也是拜片段分析地位正统化之赐，分析者因此能在严谨精确的尺度下，对一个范围明确而掌控自如的客体进行分析，而且成绩斐然。</p>
            
            <p>影片片段分析最根本的问题自然就是选择哪一个片段作分析最恰当。每个分析者的选取标准和动机各不相同，不过下面几个标准经常都在考虑之列：</p>
            
            <p>1. 选择作为分析对象的影片片段必须是一个完整的片段（它经常与前面定义过的段落或超段落相吻合）；</p>
            
            <p>2. 因应于第一点，这段影片必须在结构上紧密扎实，具有相当明显的内在组织系统；</p>
            
            <p>3. 这段影片必须足以代表整部影片，当然，其"代表性"依随个别情况的不同而更迭，没有绝对的意义，须视分析的轴线重心及分析者想要突出该片的哪个方面而定。</p>
            
            <p>影片本身在这一方面常具有决定性。我们前面所引述的，有关希区柯克、艾森斯坦或阿兰·雷奈的影片片段分析，在所选片段的整体影片代表性上都没有任何疑问，因为它们在风格上都具有统一性。"古典"电影尤其适合这类片段的抽样分析方式。</p>
            
            <p>不过，一部片子的古典程度愈弱，问题就会愈复杂，不谈实验电影或是"现代主义"电影这类不言而喻的片子，只要举《大国民》为例就可以很清楚地
        </section>
        
        <section id="section4-3">
            <h3>3. 影片开场的分析与影片分析的开始</h3>
            <p>影片开场的分析与影片分析的开始，是两个紧密相连的概念。影片的开场，通常是电影叙事的重要起点，它不仅为整个影片定下了基调，还为后续的故事发展奠定了基础。分析影片的开场，可以帮助我们理解电影的叙事结构、主题表达以及导演的风格手法。</p>
            
            <p>分析影片的开场，可以从多个角度进行。例如，可以关注开场镜头的构图、色彩、光线等视觉元素，以及这些元素如何与电影的叙事和主题相结合。此外，开场场景中的声音、音乐、对话等元素，也可以成为分析的焦点。</p>
            
            <p>影片分析的开始，则是指分析者从何时开始对影片进行分析。这个时间点可以是影片的放映开始，也可以是分析者对影片的第一次接触。分析者应该根据自己的研究目的和分析需求，确定合适的分析开始时间点。</p>
            
            <p>分析影片的开场与开始，有助于我们理解影片的叙事结构、主题表达以及导演的风格手法。通过细致的分析，我们可以更好地把握影片的整体风貌，并对其进行深入的解读。</p>
        </section>
        
        <section id="section4-4">
            <h3>4. 分析客体的范围与分析的长度</h3>
            <p>分析客体的范围与分析的长度，是两个相互关联的概念。分析客体的范围，指的是分析者所关注的影片部分或元素的范围。分析的长度，则是指分析者对影片进行分析的时间长度或篇幅长度。</p>
            
            <p>分析客体的范围，取决于分析者的研究目的和分析需求。例如，如果分析者关注的是影片的叙事结构，那么他们可能会将分析客体限定在影片的某个特定部分，如开场、高潮或结尾。如果分析者关注的是电影的风格手法，那么他们可能会将分析客体限定在影片的某个特定场景或元素上。</p>
            
            <p>分析的长度，则取决于分析者的研究时间和分析需求。例如，如果分析者希望对影片进行全面的分析，那么他们可能会选择对整个影片进行分析。如果分析者希望对影片进行简短的分析，那么他们可能会选择对影片的某个特定部分进行分析。</p>
            
            <p>分析客体的范围与分析的长度，对于分析者来说是一个需要权衡和平衡的问题。分析者应该根据自己的研究目的和分析需求，确定合适的分析客体范围和分析长度。</p>
        </section>
    </section>
    
    <section id="section5">
        <h2 id="section5">五、文本分析丰富的争议性</h2>
        <p>文本分析作为一种影片分析方法，其产生主要是为了解决影片分析面临的两个问题：在客体选择上的散漫性和在方法选择上的不确定性。文本分析方法在影片分析中占有重要地位，这主要基于两个原因：</p>
        <ul>
            <li>"文本"(texte)概念对影片及影片分析的统一性提出了最根本的问题</li>
            <li>文本分析曾几乎变成影片分析的一般代名词</li>
        </ul>
        
        <div class="key-point">
            <p>文本分析在本质上并非与其他分析方法截然不同，它也不能提供一条解决各种困难的康庄大道，但它确实为影片分析提供了一种系统化的思考框架和方法论支持。</p>
        </div>
    </section>
    
    <section id="conclusion">
        <h2 id="conclusion">结论</h2>
        <p>文本分析作为一种影片分析方法，其产生主要是为了解决影片分析面临的两个问题：在客体选择上的散漫性和在方法选择上的不确定性。文本分析方法在影片分析中占有重要地位，这主要基于两个原因：</p>
        <ul>
            <li>"文本"(texte)概念对影片及影片分析的统一性提出了最根本的问题</li>
            <li>文本分析曾几乎变成影片分析的一般代名词</li>
        </ul>
        
        <div class="key-point">
            <p>文本分析在本质上并非与其他分析方法截然不同，它也不能提供一条解决各种困难的康庄大道，但它确实为影片分析提供了一种系统化的思考框架和方法论支持。</p>
        </div>
    </section>
</body>
</html>