<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影文本分析知识体系</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --background-color: #f9f9f9;
            --text-color: #333;
            --border-color: #ddd;
        }

        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: var(--background-color);
        }

        h1, h2, h3, h4 {
            color: var(--primary-color);
            margin-top: 1.5em;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        h1 {
            text-align: center;
            font-size: 2.2em;
            color: var(--primary-color);
            border-bottom: 3px solid var(--secondary-color);
        }

        h2 {
            font-size: 1.8em;
            color: var(--secondary-color);
        }

        h3 {
            font-size: 1.5em;
            color: #2b6cb0;
        }

        .concept-box {
            background-color: #f8f9fa;
            border-left: 4px solid var(--secondary-color);
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .concept-title {
            color: var(--secondary-color);
            font-weight: bold;
            margin-bottom: 10px;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .key-point {
            color: var(--accent-color);
            font-weight: bold;
        }

        .note {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .quote {
            background-color: #f8f9fa;
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin: 15px 0;
            font-style: italic;
        }

        .knowledge-map {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .section {
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .subsection {
            margin: 20px 0;
            padding-left: 20px;
            border-left: 3px solid var(--secondary-color);
        }

        svg {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <h1>电影文本分析知识体系</h1>
    <div class="knowledge-map">
        <h2>知识体系概览</h2>
        <svg width="100%" height="500" viewBox="0 0 1000 500">
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#3498db"/>
                </marker>
            </defs>
            <!-- 标题 -->
            <rect x="400" y="20" width="200" height="50" rx="8" ry="8" fill="#2c3e50"/>
            <text x="500" y="50" text-anchor="middle" fill="white" font-size="16" font-weight="bold">电影文本分析</text>
            
            <!-- 主要分支连接线 -->
            <line x1="500" y1="70" x2="500" y2="100" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
            
            <!-- 第一层级主要概念 -->
            <rect x="200" y="100" width="180" height="50" rx="8" ry="8" fill="#3498db"/>
            <text x="290" y="130" text-anchor="middle" fill="white" font-size="14">结构主义基础</text>
            
            <rect x="410" y="100" width="180" height="50" rx="8" ry="8" fill="#3498db"/>
            <text x="500" y="130" text-anchor="middle" fill="white" font-size="14">影片文本概念</text>
            
            <rect x="620" y="100" width="180" height="50" rx="8" ry="8" fill="#3498db"/>
            <text x="710" y="130" text-anchor="middle" fill="white" font-size="14">符码分析方法</text>
            
            <!-- 连接线 -->
            <line x1="290" y1="150" x2="290" y2="180" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
            <line x1="500" y1="150" x2="500" y2="180" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
            <line x1="710" y1="150" x2="710" y2="180" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
            
            <!-- 第二层级：结构主义分支 -->
            <rect x="150" y="180" width="140" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="220" y="205" text-anchor="middle" fill="white" font-size="12">深层结构</text>
            
            <rect x="300" y="180" width="140" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="370" y="205" text-anchor="middle" fill="white" font-size="12">二元对立系统</text>
            
            <line x1="220" y1="220" x2="220" y2="250" stroke="#5dade2" stroke-width="1.5" marker-end="url(#arrowhead)"/>
            <line x1="370" y1="220" x2="370" y2="250" stroke="#5dade2" stroke-width="1.5" marker-end="url(#arrowhead)"/>
            
            <!-- 第二层级：文本概念分支 -->
            <rect x="430" y="180" width="140" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="500" y="205" text-anchor="middle" fill="white" font-size="12">文本系统</text>
            
            <rect x="430" y="230" width="140" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="500" y="255" text-anchor="middle" fill="white" font-size="12">多义性</text>
            
            <rect x="430" y="280" width="140" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="500" y="305" text-anchor="middle" fill="white" font-size="12">内涵意指</text>
            
            <!-- 第二层级：符码分析分支 -->
            <rect x="620" y="180" width="180" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="710" y="205" text-anchor="middle" fill="white" font-size="12">大组合段分析</text>
            
            <rect x="620" y="230" width="180" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="710" y="255" text-anchor="middle" fill="white" font-size="12">声音符码分析</text>
            
            <rect x="620" y="280" width="180" height="40" rx="5" ry="5" fill="#5dade2"/>
            <text x="710" y="305" text-anchor="middle" fill="white" font-size="12">片头/矩阵分析</text>
            
            <!-- 第三层级：理论家 -->
            <rect x="130" y="250" width="100" height="35" rx="5" ry="5" fill="#85c1e9"/>
            <text x="180" y="272" text-anchor="middle" fill="white" font-size="11">列维·斯特劳斯</text>
            
            <rect x="240" y="250" width="100" height="35" rx="5" ry="5" fill="#85c1e9"/>
            <text x="290" y="272" text-anchor="middle" fill="white" font-size="11">艾柯</text>
            
            <rect x="350" y="250" width="100" height="35" rx="5" ry="5" fill="#85c1e9"/>
            <text x="400" y="272" text-anchor="middle" fill="white" font-size="11">罗兰·巴特</text>
            
            <rect x="600" y="330" width="100" height="35" rx="5" ry="5" fill="#85c1e9"/>
            <text x="650" y="352" text-anchor="middle" fill="white" font-size="11">超段落</text>
            
            <rect x="710" y="330" width="100" height="35" rx="5" ry="5" fill="#85c1e9"/>
            <text x="760" y="352" text-anchor="middle" fill="white" font-size="11">次段落</text>
            
            <!-- 连接线 -->
            <line x1="710" y1="320" x2="650" y2="330" stroke="#5dade2" stroke-width="1.5" marker-end="url(#arrowhead)"/>
            <line x1="710" y1="320" x2="760" y2="330" stroke="#5dade2" stroke-width="1.5" marker-end="url(#arrowhead)"/>
            
            <!-- 底部连接 -->
            <rect x="350" y="400" width="300" height="45" rx="8" ry="8" fill="#e74c3c"/>
            <text x="500" y="428" text-anchor="middle" fill="white" font-size="14" font-weight="bold">文本分析的开放性与争议性</text>
            
            <line x1="290" y1="320" x2="420" y2="400" stroke="#3498db" stroke-width="1.5" stroke-dasharray="5,3" marker-end="url(#arrowhead)"/>
            <line x1="500" y1="320" x2="500" y2="400" stroke="#3498db" stroke-width="1.5" stroke-dasharray="5,3" marker-end="url(#arrowhead)"/>
            <line x1="710" y1="365" x2="580" y2="400" stroke="#3498db" stroke-width="1.5" stroke-dasharray="5,3" marker-end="url(#arrowhead)"/>
        </svg>
    </div>

    <div class="section" id="introduction">
        <h2>引言</h2>
        <p>文本分析作为一种影片分析方法，产生于对分析工具、分析客体和分析途径等多重选择可能性的应对需求。它在当代电影分析中的重要地位主要基于两个原因：</p>
        <ul>
            <li>"文本"概念对影片及影片分析的统一性提出了根本问题</li>
            <li>文本分析曾几乎成为影片分析的一般代名词</li>
        </ul>
    </div>

    <div class="section" id="structuralism">
        <h2>一、文本分析与结构主义</h2>
        
        <div class="concept-box">
            <div class="concept-title">结构主义的核心概念</div>
            <p>结构主义(structuralisme)是20世纪60年代盛行的思潮，其核心是"结构"概念。结构主义分析家试图辨明隐藏在意义生产过程中的<span class="highlight">深层结构</span>(structure "profonde")。</p>
        </div>

        <div class="subsection">
            <h3>1. 基本概念</h3>
            
            <div class="note">
                <p>列维·斯特劳斯的贡献：</p>
                <ul>
                    <li>发现表面复杂的神话具有规律性和系统性</li>
                    <li>推论出不同的意义生产活动可能共享相同的结构</li>
                    <li>建立了"神话素"(mythemes)体系</li>
                </ul>
            </div>

            <div class="quote">
                "不论在思辨层面或实践层面，差别的明显性较其内容更重要得多：只要它存在，就会形成一种有用的区别系统，如同一个可以运用在一个初窥乍看之下完续而难以理解的文本之上的架构。" —— 列维·斯特劳斯《野性的思维》
            </div>

            <div class="concept-box">
                <div class="concept-title">结构主义的关键特征</div>
                <ul>
                    <li>强调<span class="key-point">二元对立系统</span>(oppositions binaires)</li>
                    <li>以语言学为理论基础和参考坐标</li>
                    <li>关注意义生产过程的基底架构</li>
                </ul>
            </div>
        </div>

        <div class="subsection">
            <h3>2. 结构主义分析的应用</h3>
            
            <div class="concept-box">
                <div class="concept-title">重要理论家及其贡献</div>
                <ul>
                    <li><span class="key-point">艾柯</span>：提出意义与沟通现象组成符号系统的观念</li>
                    <li><span class="key-point">罗兰·巴特</span>：
                        <ul>
                            <li>分析广告影像中的内涵意旨</li>
                            <li>探讨影片内涵意义的位置、形式与效果</li>
                        </ul>
                    </li>
                    <li><span class="key-point">梅斯</span>：
                        <ul>
                            <li>系统化回答影片意义问题</li>
                            <li>定义符码概念，涵盖影片表意作用的规律</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <div class="note">
                <p>符码概念的特点：</p>
                <ul>
                    <li>可独立成"纯粹状态"，但永远以共生方式结合运作</li>
                    <li>描述电影语言活动内部的多重表意功能</li>
                    <li>既可检视普遍现象，也可检视局部现象</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section" id="film-text">
        <h2>二、影片文本</h2>
        
        <div class="concept-box">
            <div class="concept-title">基本概念三要素</div>
            <ul>
                <li><span class="key-point">影片文本</span>(le texte filmique)：作为"言谈齐一性之实际体现"的具体影片</li>
                <li><span class="key-point">影片文本系统</span>(le systeme textuel filmique)：每部影片所独有的结构模式</li>
                <li><span class="key-point">符码</span>(le code)：可运用在不同文本上的普遍性系统</li>
            </ul>
            
            <div style="margin-top: 20px;">
                <svg width="100%" height="300" viewBox="0 0 800 300">
                    <!-- 背景 -->
                    <rect x="50" y="20" width="700" height="260" rx="10" ry="10" fill="#f8f9fa" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- 影片文本 -->
                    <rect x="100" y="60" width="180" height="180" rx="8" ry="8" fill="#3498db" opacity="0.8"/>
                    <text x="190" y="45" text-anchor="middle" fill="#2c3e50" font-weight="bold">影片文本</text>
                    <text x="190" y="150" text-anchor="middle" fill="white" font-size="14">具体影片</text>
                    <text x="190" y="180" text-anchor="middle" fill="white" font-size="12">电影语言符码的</text>
                    <text x="190" y="200" text-anchor="middle" fill="white" font-size="12">具体组合与运用</text>
                    
                    <!-- 影片文本系统 -->
                    <rect x="310" y="60" width="180" height="180" rx="8" ry="8" fill="#2c3e50" opacity="0.8"/>
                    <text x="400" y="45" text-anchor="middle" fill="#2c3e50" font-weight="bold">影片文本系统</text>
                    <text x="400" y="140" text-anchor="middle" fill="white" font-size="14">每部影片所独有</text>
                    <text x="400" y="170" text-anchor="middle" fill="white" font-size="14">的结构模式</text>
                    <text x="400" y="200" text-anchor="middle" fill="white" font-size="12">分析家构建</text>
                    
                    <!-- 符码 -->
                    <rect x="520" y="60" width="180" height="180" rx="8" ry="8" fill="#e74c3c" opacity="0.8"/>
                    <text x="610" y="45" text-anchor="middle" fill="#2c3e50" font-weight="bold">符码</text>
                    <text x="610" y="140" text-anchor="middle" fill="white" font-size="14">普遍性系统</text>
                    <text x="610" y="170" text-anchor="middle" fill="white" font-size="14">可应用于多种文本</text>
                    <text x="610" y="200" text-anchor="middle" fill="white" font-size="12">规律与系统化现象</text>
                    
                    <!-- 连接箭头 -->
                    <line x1="280" y1="150" x2="310" y2="150" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="490" y1="150" x2="520" y2="150" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- 关系说明 -->
                    <text x="295" y="130" text-anchor="middle" fill="#333" font-size="11">分析</text>
                    <text x="505" y="130" text-anchor="middle" fill="#333" font-size="11">应用</text>
                    
                    <!-- 层级关系 -->
                    <line x1="100" y1="250" x2="700" y2="250" stroke="#333" stroke-width="1" stroke-dasharray="5,3"/>
                    <text x="190" y="275" text-anchor="middle" fill="#3498db" font-size="12" font-weight="bold">具体实例</text>
                    <text x="400" y="275" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">特定结构</text>
                    <text x="610" y="275" text-anchor="middle" fill="#e74c3c" font-size="12" font-weight="bold">普遍规律</text>
                </svg>
            </div>
        </div>

        <div class="subsection">
            <h3>1. 文本概念的演变</h3>
            
            <div class="concept-box">
                <div class="concept-title">理论发展脉络</div>
                <ul>
                    <li>茱莉亚·克丽斯特娃：
                        <ul>
                            <li>文本是笔体本身的"空间"</li>
                            <li>视文本为意义生产的无尽过程</li>
                        </ul>
                    </li>
                    <li>罗兰·巴特：
                        <ul>
                            <li>提出文本"抄体"(scriptible)概念</li>
                            <li>强调作品的"复数性"("pluriel" d'une oeuvre)</li>
                            <li>发展内涵意指(connotation)分析工具</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <div class="note">
                <p>巴特的《S/Z》重要贡献：</p>
                <ul>
                    <li>提出对文本既非"主观"也非"客观"的阅读态度</li>
                    <li>发展"慢速"分析方式</li>
                    <li>通过词组(lexie)概念进行分析</li>
                    <li>保留文本表意系统的多义特质</li>
                </ul>
                
                <div style="margin-top: 20px;">
                    <svg width="100%" height="350" viewBox="0 0 700 350">
                        <!-- 背景 -->
                        <rect x="50" y="20" width="600" height="310" rx="10" ry="10" fill="#f8f9fa" stroke="#ddd" stroke-width="1"/>
                        
                        <!-- 标题 -->
                        <text x="350" y="45" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">巴特《S/Z》的文本分析方法</text>
                        
                        <!-- 文本 -->
                        <rect x="100" y="70" width="500" height="40" rx="5" ry="5" fill="#2c3e50" opacity="0.8"/>
                        <text x="350" y="95" text-anchor="middle" fill="white" font-size="14" font-weight="bold">文本整体</text>
                        
                        <!-- 词组分割 -->
                        <rect x="100" y="130" width="80" height="30" rx="3" ry="3" fill="#3498db" opacity="0.8"/>
                        <rect x="190" y="130" width="60" height="30" rx="3" ry="3" fill="#3498db" opacity="0.8"/>
                        <rect x="260" y="130" width="100" height="30" rx="3" ry="3" fill="#3498db" opacity="0.8"/>
                        <rect x="370" y="130" width="70" height="30" rx="3" ry="3" fill="#3498db" opacity="0.8"/>
                        <rect x="450" y="130" width="150" height="30" rx="3" ry="3" fill="#3498db" opacity="0.8"/>
                        <text x="140" y="150" text-anchor="middle" fill="white" font-size="12">词组1</text>
                        <text x="220" y="150" text-anchor="middle" fill="white" font-size="12">词组2</text>
                        <text x="310" y="150" text-anchor="middle" fill="white" font-size="12">词组3</text>
                        <text x="405" y="150" text-anchor="middle" fill="white" font-size="12">词组4</text>
                        <text x="525" y="150" text-anchor="middle" fill="white" font-size="12">词组5</text>
                        
                        <!-- 分析过程 -->
                        <line x1="140" y1="160" x2="140" y2="180" stroke="#333" stroke-width="1.5" marker-end="url(#arrowhead)"/>
                        
                        <!-- 符码 -->
                        <rect x="80" y="180" width="120" height="100" rx="5" ry="5" fill="#f8f9fa" stroke="#ddd" stroke-width="1"/>
                        <text x="140" y="200" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">词组分析</text>
                        <text x="140" y="220" text-anchor="middle" fill="#e74c3c" font-size="11">内涵意指 1</text>
                        <text x="140" y="240" text-anchor="middle" fill="#e74c3c" font-size="11">内涵意指 2</text>
                        <text x="140" y="260" text-anchor="middle" fill="#e74c3c" font-size="11">内涵意指 3</text>
                        
                        <!-- 符码系统 -->
                        <rect x="250" y="210" width="300" height="100" rx="8" ry="8" fill="#e74c3c" opacity="0.7"/>
                        <text x="400" y="235" text-anchor="middle" fill="white" font-size="14" font-weight="bold">五大符码系统</text>
                        <text x="400" y="260" text-anchor="middle" fill="white" font-size="12">指涉性符码、诠释符码、象征符码</text>
                        <text x="400" y="280" text-anchor="middle" fill="white" font-size="12">语义素符码、行动结果确立符码</text>
                        <text x="400" y="300" text-anchor="middle" fill="white" font-size="12">每个内涵意指归属到符码系统中</text>
                        
                        <!-- 连接线 -->
                        <line x1="200" y1="230" x2="250" y2="230" stroke="#333" stroke-width="1.5" marker-end="url(#arrowhead)"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>2. 影片的文本分析</h3>
            
            <div class="concept-box">
                <div class="concept-title">分析特点</div>
                <ul>
                    <li>拒绝对文本做出总结性诠释</li>
                    <li>保持文本表意系统的开放性</li>
                    <li>重视分析过程中的再阅读(relecture)</li>
                </ul>
            </div>

            <div class="note">
                <p>实践案例：</p>
                <ul>
                    <li>蒂埃里·昆塞尔对《M》的片头分析：
                        <ul>
                            <li>采用巴特的词组分析方法</li>
                            <li>灵活运用多种符码类型</li>
                            <li>强调分析的开放性</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section" id="film-code">
        <h2>三、影片符码分析</h2>
        
        <div class="concept-box">
            <div class="concept-title">符码分析的基本问题</div>
            <ul>
                <li>符码的普遍性与特殊性</li>
                <li>符码的纯粹状态与共生状态</li>
                <li>符码对艺术内在特质的把握能力</li>
            </ul>
        </div>

        <div class="subsection">
            <h3>1. 影片分析与符码系统分析</h3>
            
            <div class="concept-box">
                <div class="concept-title">分段分析方法</div>
                <ul>
                    <li><span class="key-point">大组合段</span>：
                        <ul>
                            <li>作为影片分析的基本单位</li>
                            <li>反映影片的风格特性</li>
                            <li>体现电影形式发展阶段</li>
                        </ul>
                    </li>
                    <li><span class="key-point">分段层次</span>：
                        <ul>
                            <li>超段落(unités sur-segmentales)</li>
                            <li>次段落(unités sous-segmentales)</li>
                        </ul>
                    </li>
                </ul>
                
                <div style="margin-top: 20px;">
                    <svg width="100%" height="300" viewBox="0 0 700 300">
                        <!-- 背景 -->
                        <rect x="50" y="20" width="600" height="260" rx="10" ry="10" fill="#f8f9fa" stroke="#ddd" stroke-width="1"/>
                        
                        <!-- 标题 -->
                        <text x="350" y="45" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">分段层次关系</text>
                        
                        <!-- 超段落 -->
                        <rect x="100" y="80" width="500" height="40" rx="5" ry="5" fill="#3498db" opacity="0.8"/>
                        <text x="350" y="105" text-anchor="middle" fill="white" font-size="14" font-weight="bold">超段落（叙事单元）</text>
                        
                        <!-- 大组合段 -->
                        <rect x="100" y="140" width="150" height="40" rx="5" ry="5" fill="#2c3e50" opacity="0.8"/>
                        <rect x="270" y="140" width="150" height="40" rx="5" ry="5" fill="#2c3e50" opacity="0.8"/>
                        <rect x="440" y="140" width="160" height="40" rx="5" ry="5" fill="#2c3e50" opacity="0.8"/>
                        <text x="175" y="165" text-anchor="middle" fill="white" font-size="12">大组合段 1</text>
                        <text x="345" y="165" text-anchor="middle" fill="white" font-size="12">大组合段 2</text>
                        <text x="520" y="165" text-anchor="middle" fill="white" font-size="12">大组合段 3</text>
                        
                        <!-- 次段落 -->
                        <rect x="100" y="200" width="70" height="30" rx="3" ry="3" fill="#e74c3c" opacity="0.8"/>
                        <rect x="180" y="200" width="70" height="30" rx="3" ry="3" fill="#e74c3c" opacity="0.8"/>
                        <rect x="270" y="200" width="70" height="30" rx="3" ry="3" fill="#e74c3c" opacity="0.8"/>
                        <rect x="350" y="200" width="70" height="30" rx="3" ry="3" fill="#e74c3c" opacity="0.8"/>
                        <rect x="440" y="200" width="70" height="30" rx="3" ry="3" fill="#e74c3c" opacity="0.8"/>
                        <rect x="520" y="200" width="80" height="30" rx="3" ry="3" fill="#e74c3c" opacity="0.8"/>
                        <text x="135" y="220" text-anchor="middle" fill="white" font-size="10">次段落1.1</text>
                        <text x="215" y="220" text-anchor="middle" fill="white" font-size="10">次段落1.2</text>
                        <text x="305" y="220" text-anchor="middle" fill="white" font-size="10">次段落2.1</text>
                        <text x="385" y="220" text-anchor="middle" fill="white" font-size="10">次段落2.2</text>
                        <text x="475" y="220" text-anchor="middle" fill="white" font-size="10">次段落3.1</text>
                        <text x="560" y="220" text-anchor="middle" fill="white" font-size="10">次段落3.2</text>
                        
                        <!-- 说明 -->
                        <text x="350" y="260" text-anchor="middle" fill="#2c3e50" font-size="12">分段分析通过不同层级揭示影片的风格特性和结构</text>
                    </svg>
                </div>
            </div>

            <div class="note">
                <p>声音符码分析特点：</p>
                <ul>
                    <li>不具独立自主功能</li>
                    <li>具有复数形式</li>
                    <li>涉及音响构成、音画关系等多个层面</li>
                </ul>
            </div>
        </div>

        <div class="subsection">
            <h3>2. 影片开场的分析</h3>
            
            <div class="concept-box">
                <div class="concept-title">片头分析的重要性</div>
                <ul>
                    <li>作为影片"矩阵"(matrice)的功能</li>
                    <li>决定观众入戏状态</li>
                    <li>建立虚构世界的效果</li>
                </ul>
                
                <div style="margin-top: 20px;">
                    <svg width="100%" height="320" viewBox="0 0 700 320">
                        <!-- 背景 -->
                        <rect x="50" y="20" width="600" height="280" rx="10" ry="10" fill="#f8f9fa" stroke="#ddd" stroke-width="1"/>
                        
                        <!-- 标题 -->
                        <text x="350" y="45" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">影片片头作为矩阵的分析</text>
                        
                        <!-- 影片片头 -->
                        <rect x="150" y="70" width="400" height="60" rx="5" ry="5" fill="#2c3e50" opacity="0.8"/>
                        <text x="350" y="105" text-anchor="middle" fill="white" font-size="14" font-weight="bold">影片片头（矩阵）</text>
                        
                        <!-- 箭头 -->
                        <line x1="350" y1="130" x2="350" y2="150" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- 扩展分析 -->
                        <rect x="100" y="150" width="500" height="40" rx="5" ry="5" fill="#3498db" opacity="0.8"/>
                        <text x="350" y="175" text-anchor="middle" fill="white" font-size="14">影片整体的结构规则与主题预示</text>
                        
                        <!-- 具体分析元素 -->
                        <rect x="100" y="210" width="130" height="40" rx="3" ry="3" fill="#e74c3c" opacity="0.7"/>
                        <text x="165" y="235" text-anchor="middle" fill="white" font-size="12">虚构状态建立</text>
                        
                        <rect x="250" y="210" width="200" height="40" rx="3" ry="3" fill="#e74c3c" opacity="0.7"/>
                        <text x="350" y="235" text-anchor="middle" fill="white" font-size="12">叙事模式与观众期待设定</text>
                        
                        <rect x="470" y="210" width="130" height="40" rx="3" ry="3" fill="#e74c3c" opacity="0.7"/>
                        <text x="535" y="235" text-anchor="middle" fill="white" font-size="12">符号暗示系统</text>
                        
                        <!-- 连接线 -->
                        <line x1="165" y1="190" x2="165" y2="210" stroke="#3498db" stroke-width="1.5" marker-end="url(#arrowhead)"/>
                        <line x1="350" y1="190" x2="350" y2="210" stroke="#3498db" stroke-width="1.5" marker-end="url(#arrowhead)"/>
                        <line x1="535" y1="190" x2="535" y2="210" stroke="#3498db" stroke-width="1.5" marker-end="url(#arrowhead)"/>
                        
                        <!-- 说明 -->
                        <text x="350" y="280" text-anchor="middle" fill="#2c3e50" font-size="12">片头分析揭示整部影片的叙事规则与结构原型</text>
                    </svg>
                </div>
            </div>

            <div class="note">
                <p>分析方法特点：</p>
                <ul>
                    <li>关注结构的完整性</li>
                    <li>探究叙事开场的语义特质</li>
                    <li>分析虚构状态的建立过程</li>
                </ul>
            </div>
        </div>

        <div class="subsection">
            <h3>3. 分析的范围与长度</h3>
            
            <div class="concept-box">
                <div class="concept-title">分析实践要点</div>
                <ul>
                    <li>分析长度取决于分析客体而非文本长度</li>
                    <li>需要区分分析客体与物质素材</li>
                    <li>可采用多种分析策略组合</li>
                </ul>
            </div>

            <div class="note">
                <p>分析策略类型：</p>
                <ul>
                    <li>片段分析</li>
                    <li>符码研究</li>
                    <li>语境研究</li>
                    <li>整体分析</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section" id="conclusion">
        <h2>结语：文本分析的争议性</h2>
        
        <div class="concept-box">
            <div class="concept-title">主要批评与回应</div>
            <ul>
                <li>适用范围的限制</li>
                <li>过度分析的倾向</li>
                <li>忽视语境的问题</li>
                <li>简约化的危险</li>
            </ul>
        </div>

        <div class="note">
            <p>文本分析的价值：</p>
            <ul>
                <li>提供了系统的分析框架</li>
                <li>开启了多种研究路径</li>
                <li>保持了分析的开放性</li>
                <li>促进了电影理论的发展</li>
            </ul>
        </div>
    </div>
</body>
</html> 