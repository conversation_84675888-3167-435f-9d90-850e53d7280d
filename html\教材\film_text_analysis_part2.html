<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影文本分析概要 - 第三章（第二部分）</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.3em;
        }
        h1 {
            text-align: center;
            font-size: 2.2em;
            color: #1a365d;
        }
        h2 {
            font-size: 1.8em;
            color: #2c5282;
        }
        h3 {
            font-size: 1.5em;
            color: #2b6cb0;
        }
        blockquote {
            background-color: #f0f4f8;
            border-left: 4px solid #4299e1;
            padding: 10px 15px;
            margin: 20px 0;
        }
        code {
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background-color: #ebf4ff;
            padding: 2px 0;
        }
        .concept {
            color: #3182ce;
            font-weight: bold;
        }
        .example {
            background-color: #e6fffa;
            padding: 15px;
            border-left: 4px solid #38b2ac;
            margin: 20px 0;
        }
        .note {
            background-color: #fffaf0;
            padding: 15px;
            border-left: 4px solid #ed8936;
            margin: 20px 0;
        }
        .diagram {
            text-align: center;
            margin: 20px 0;
        }
        svg {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <h1>第三章 文本分析——一个引人争议的模式（第二部分）</h1>
    
    <div class="section">
        <h2>二、影片文本</h2>
        
        <h3>1. 文本概念的演变</h3>
        <p>影片分析从电影结构主义符号学(semiologie structurale)汲取了三个基本概念：</p>
        
        <ol>
            <li><span class="concept">影片文本</span>(le texte filmique)：作为"言谈齐一性(unité de discours)之实际体现"的具体影片，即电影语言符码的具体组合与运用；</li>
            <li><span class="concept">影片文本系统</span>(le systeme textuel filmique)：每部影片所独有的结构模式，由分析家建构，是一种接受该文本固有逻辑性与紧密性制约的特定符码组合方式；</li>
            <li><span class="concept">符码</span>(le code)：本身也是一种由关系与区别形成的系统，但与影片文本系统不同，符码是一种可运用在不同文本上、更具普遍性的系统（而每个文本则成为该符码的"信息"[message]）。</li>
        </ol>
        
        <div class="diagram">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <rect x="50" y="20" width="500" height="70" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                <text x="300" y="55" text-anchor="middle" font-size="20" fill="#2d3748">影片文本 (le texte filmique)</text>
                <text x="300" y="80" text-anchor="middle" font-size="14" fill="#4a5568">具体影片，电影语言符码的具体组合与运用</text>
                
                <rect x="50" y="110" width="500" height="70" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                <text x="300" y="145" text-anchor="middle" font-size="20" fill="#2d3748">影片文本系统 (le systeme textuel filmique)</text>
                <text x="300" y="170" text-anchor="middle" font-size="14" fill="#4a5568">影片独有的结构模式</text>
                
                <rect x="50" y="200" width="500" height="70" rx="5" ry="5" fill="#e2e8f0" stroke="#4a5568" stroke-width="2"/>
                <text x="300" y="235" text-anchor="middle" font-size="20" fill="#2d3748">符码 (le code)</text>
                <text x="300" y="260" text-anchor="middle" font-size="14" fill="#4a5568">具普遍性的系统，可应用于不同文本</text>
                
                <line x1="300" y1="90" x2="300" y2="110" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="300" y1="180" x2="300" y2="200" stroke="#4a5568" stroke-width="2" stroke-dasharray="5,5"/>
            </svg>
        </div>
        
        <p>文本除了上述结构主义意涵外，在20世纪60年代末期也特别指称现代（文学范畴的）文本。茱莉亚·克丽斯特娃(Julia Kristeva)在《Tel Quel》上提出文本定义，认为文本不是陈列在书店里的作品，而是笔体(ecriture)本身的"空间"(espace)。在这一严谨定义下，文本被视为意义生产的（无尽的）过程，潜在着无尽且无数的阅读空间活动，是现代文学文本生产力(productivité)的构成要素。</p>
        
        <p>这一"文本"观念并未立即被影片学接受，原因有二：</p>
        <ul>
            <li>它成为一个狭义概念，基本上不能适应所有文学或电影作品</li>
            <li>它假设读者扮演与作者同样主动、具"生产力"的角色（巴特甚至认为在这意义下，"文本"模式就是"我们正在书写的……无止境的现在"）</li>
        </ul>
        
        <p>然而，不论影片实验性有多强，它永远受到特定条件制约，尤其是影片运转更约束观众做出积极的"参与"或"合作"。尽管从"开放式"电影到"非叙事性"电影或"结构式"电影等尝试多样，电影呈现给观众的，终究是一个具有特定组合次序及固定速度的已完成产品(produit fini)。</p>
        
        <p>文本肌理(textualité)观念之所以深入影片分析领域，主要得益于罗兰·巴特的重要分析名作《S/Z》(1970)。在这本分析巴尔扎克短篇小说《萨拉辛纳》的专论开头，巴特提出了理论折中做法。他把文本"抄体"(scriptible)视为作品完结的否定，而以一个更具运作性的作品"复数性"("pluriel" d'une oeuvre)取代，即文学（特别是古典文学）不是由抄写的文本组成，而是由阅读的(lisible)作品组成。</p>
        
        <p>文本理想上无法企及绝对无尽的复数性，但有些作品显示出"有限的复数性"，即巴特所称的<span class="concept">多义性</span>(polysémie)。因此，分析家任务是"拆散"、"摊平"文本以呈现其复数性、多义性，而善用<span class="concept">内涵意指</span>(connotation)是完成这类分析性阅读的有效工具。只有系统性阅读才能保证分析得到的内涵意指客观中肯，免于穿凿附会。巴特认为每个内涵意指基本上都是"符码的起点"(depart d'un code)，并在五个有限符码上进行《萨拉辛纳》的阅读分析。</p>
        
        <p>在这些前提下产生了几个重要理论结果：《S/Z》采取对阅读古典文本既非"主观"也非"客观"的态度（"既不停留在文本层次、也不停留在主观我层次的系统移转"阅读方式）。这样的阅读方式并非不完整，因为它在特定逻辑上开展，主要目标不在描述该文本结构（"没有所谓的文本'纲要'[somme]可言"），且永远是未完成的阅读（"一切都在不断地显现意义"）。</p>
        
        <p>巴特实际运用的分析方法成果丰硕：为彻底否定文本完结于某种最终诠释的封闭传统，巴特以"慢速"分析方式展现古典文本结构的可逆性。这里涉及的基本概念是<span class="concept">词组</span>(lexie)，一个由分析家自行规划的、长短不拘的文本片段(fragment de texte)。整个分析过程按顺序检视这些词组，指出内涵意指的能指单元(unités signifiantes)，再将每个内涵意指归属到五个一般符码系统中。这篇论文最令人激赏之处是，不仅原则上拒绝总结，还以更自由开阔方式保留文本表意系统的多义特质，形成"容量分析"式阅读方式，打破作品常态性。</p>
        
        <p>《S/Z》中的符码概念比较松散，虽大部分在梅斯的《语言活动与电影》中得到印证，但范围更宽松。巴特的"指涉性"符码(code referentiel)及"象征性"符码(code symbolique)实际上是各种不同内涵意指的总集合体（《S/Z》有时把后者称为象征性"场域"而非符码）。到1973年，巴特分析爱伦坡的《范德美先生》时，将符码定义为"超文本的(supra-textuel)、制定结构概念组织标记的联合场域"，此后符码类别清单既无法终结也无法固定，需视所分析文本而定。</p>
        
        <h3>2. 影片的文本分析</h3>
        <p>巴特的《S/Z》成功融合了文本概念的两层含义，推动了影片文本分析的发展。关于指出文本表意成分、舒张隐含内涵意指、正确鉴别潜在符码等基本文本概念，在梅斯的分析实践中得以延伸。但巴特模式的主要魅力来自他背离传统建构固定、详尽评述某一文本的研究系统，代之以"开放"态度，摒弃分析必须在最终意义上划句点的研究方法。</p>
        
        <p>蒂埃里·昆塞尔(Thierry Kuntzel)对弗里茨·朗的《M》所作的片头分析(1972年)是早期典范。方法论上，昆塞尔完全采用巴特的分析过程——把影片文本按事件发展顺序切分成词组，不理会组合段或叙事空间的"技术性"间歇，将片头分为三个长度不等的词组：</p>
        <ol>
            <li>影片字幕部分（特别是标题）</li>
            <li>第一个镜头（孩子唱"坏人来了"儿歌，妇人提洗衣篮）</li>
            <li>该片段其余全部镜头</li>
        </ol>
        
        <p>虽然这种区分有些故作惊人（其实再分隔"第三个词组"很容易），但昆塞尔主要想显示分镜并没有绝对价值，只是针对特定分析准备的表现素材。</p>
        
        <p>这篇论述的第二个特性是借由一定数量和类型的符码界定每个词组功能。第一个词组有：叙述符码(code narratif，遵循剧情片从第一画面开始展开虚构空间的惯例)、诠释符码(code herméneutique，指"M"字之谜)和象征符码(code symbolique，昆塞尔将"M"字母与"M字母直划部分"主题衔接)。第二个词组突出视觉符码（摄影机运动、画面构图等）及叙述符码（引申巴特的"指涉性符码"）。第三个词组则集中在如何借纯电影符码(codes cinematographiques，尤其是蒙太奇符码)表现焦心期待与期待落空的主题。</p>
        
        <p>分析者自由界定这些符码的做法颇不寻常。一方面，他撷取巴特《S/Z》的五大符码系统作分析基石，但不无问题，如"语义素符码"(code semantique)成为收集疑难杂症的框架，而"行动结果确立符码"(code proairetique)不适合电影，因为电影中行动不是被描述而是被表现。另一方面，有些符码直接借自梅斯的《语言活动与电影》，如"构成符码"、"摄影机运动符码"、"摄影角度符码"等，还有一些普遍性的符码类型，如从巴特处得到灵感的"叙述符码"，或"视线符码"、"布景符码"等特殊符码。这表明文本模式内部并无完备的符码清单可套用，相反，文本模式要求分析者在每个能指元素上引入"可能发展的符码"。</p>
        
        <p>这篇《M》片分析还有一个值得借鉴之处：虽然昆塞尔按影片叙事顺序阅读，但一直援引影片后续部分与每个词组（尤其前两个）互相关照。例如，M字母的"直划部分"主题与后续内容相互关联，从有活动关节的玩偶及以玩偶双足框出凶手脸部等处表现。巴特强调的分析必须是一种"再阅读"(relecture)在此得到印证。这篇论文几乎是最早"运用"文本分析概念分析影片的理想表征，明确显示文本分析绝非单纯"运用"一个"模式"就算了事。</p>
    </div>
</body>
</html> 