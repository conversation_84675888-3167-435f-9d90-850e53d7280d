<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金马奖十年深度分析（2010-2020）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border-radius: 15px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        h1 {
            color: #8e44ad;
            text-align: center;
            font-size: 2.8rem;
            margin-bottom: 1rem;
            padding: 30px 0;
            background: linear-gradient(45deg, #8e44ad, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.3rem;
            margin-bottom: 3rem;
            font-style: italic;
        }

        .nav-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .nav-title {
            color: white;
            font-size: 1.4rem;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }

        .nav-item {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-top: 3rem;
            margin-bottom: 1.5rem;
            padding-bottom: 12px;
            border-bottom: 4px solid #8e44ad;
        }

        h3 {
            color: #34495e;
            font-size: 1.5rem;
            margin-top: 2.5rem;
            margin-bottom: 1.2rem;
            padding-left: 20px;
            border-left: 5px solid #3498db;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 6px solid #8e44ad;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .analysis-point {
            margin: 18px 0;
            padding: 18px;
            background: #fefefe;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .quote {
            font-style: italic;
            color: #7f8c8d;
            background: #f9f9f9;
            padding: 18px;
            border-radius: 8px;
            margin: 18px 0;
            border-left: 5px solid #f39c12;
        }

        .data-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .data-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .data-content {
            font-size: 1.3rem;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            h1 {
                font-size: 2.2rem;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>金马奖十年深度分析</h1>
        <div class="subtitle">华语电影的重要风向标（2010-2020）</div>

        <div class="nav-container">
            <div class="nav-title">📋 内容导航</div>
            <div class="nav-grid">
                <a href="#overview" class="nav-item">金马奖概述</a>
                <a href="#cultural-radiation" class="nav-item">文化辐射</a>
                <a href="#freedom" class="nav-item">创作自由</a>
                <a href="#family-films" class="nav-item">家庭片倾向</a>
                <a href="#balance" class="nav-item">平衡之道</a>
                <a href="#major-events" class="nav-item">重大事件</a>
            </div>
        </div>

        <section id="overview">
            <h2>🌍 金马奖概述</h2>
            
            <div class="highlight">
                <strong>核心定位</strong>：金马奖作为华语电影的重要奖项，在这十年间经历了重要的转型，从单纯的台湾地区奖项发展为辐射整个华语文化圈的重要平台。
            </div>

            <div class="analysis-point">
                <h4>承前启后的作用</h4>
                <p>金马奖十年专题是华语片十年专题回顾的第二期，与港片十年形成互补关系，同时也为后续的国产类型片专题做铺垫。</p>
            </div>

            <div class="analysis-point">
                <h4>评选范围的变化</h4>
                <p>从最初的台湾地区奖项，扩展到涵盖所有华语地区，再到后来将范围扩大到"华人"概念，只要是由华人导演拍摄的华人故事，都可以纳入金马奖的评选范围。</p>
            </div>
        </section>

        <section id="cultural-radiation">
            <h2>🌟 文化辐射</h2>

            <div class="highlight">
                <strong>文化影响力</strong>：金马奖在这十年间扮演了类似戛纳电影节在法语区的角色，成为艺术片和作者电影的培养皿。
            </div>

            <div class="analysis-point">
                <h4>辐射范围的扩大</h4>
                <p>从华语地区扩展到所有有华人存在的国家，不再局限于语言，而是以文化认同为核心。</p>
            </div>

            <div class="analysis-point">
                <h4>重要标志性事件</h4>
                <p>第50届金马奖上，新加坡电影《爸妈不在家》击败《一代宗师》等重量级作品获得最佳影片，成为金马奖转型的重要标志。</p>
            </div>

            <div class="quote">
                "金马奖不再局限于说王家卫跟陈正义谁好的问题，而是更加宏观的一个最重要的信号。"
            </div>

            <div class="data-box">
                <div class="data-title">收视率数据</div>
                <div class="data-content">第50届金马奖收视率：9.12（历史最高）</div>
            </div>
        </section>
    </div>
</body>
</html> 